#!/bin/bash

# TuringTransformer Integration Test Runner
# 
# This script runs the integration tests for the refactored TuringTransformer
# using real ComparisEFAG.csv data.
#
# Usage: ./run_integration_tests.sh

set -e  # Exit on any error

echo "🚀 TuringTransformer Integration Test Runner"
echo "============================================="

# Check if we're in the right directory
if [ ! -f "tests/integration_test_turing_transformer.py" ]; then
    echo "❌ Error: Please run this script from the dags directory"
    echo "   Expected location: application/airflow/dags/"
    exit 1
fi

# Check if test data exists
if [ ! -f "input_parameters/comparis/comparis_motor/ComparisEFAG_test.csv" ]; then
    echo "❌ Error: Test data file not found"
    echo "   Expected: input_parameters/comparis/comparis_motor/ComparisEFAG_test.csv"
    exit 1
fi

echo "📁 Test data found: ComparisEFAG_test.csv"
echo "🧪 Running integration tests..."
echo ""

# Run the integration test
python tests/integration_test_turing_transformer.py

# Check the exit code
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Integration tests completed successfully!"
    echo "📄 Sample request saved to: tests/sample_turing_request.json"
    echo ""
    echo "✅ The refactored TuringTransformer is working correctly with real data."
else
    echo ""
    echo "❌ Integration tests failed!"
    echo "Please check the logs above for details."
    exit 1
fi
