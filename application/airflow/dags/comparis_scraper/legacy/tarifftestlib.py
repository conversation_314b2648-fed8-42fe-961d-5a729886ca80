from collections import abc

import numpy as np
import pandas as pd

import mappers_global as mappers_global

def execution_time_decorator(func):
    import time
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        elapsed_time = end_time - start_time
        print(f"Execution time of {func.__name__}: {elapsed_time:.4f} seconds")
        return result

    return wrapper

TuringResponseTopLevelStructure = {
                                   'quote.product.id': {'newname': 'ProductId', 'drop': False},
                                   'quote.product.version': {'newname': 'ProductVersion', 'drop': False},
                                   'quote.pricingVersion': {'newname': 'PricingVersion', 'drop': False},
                                   'quote.status': {'newname': 'Status', 'drop': True},
                                   'quote.operationType': {'newname': 'QuoteOperationType', 'drop': True},
                                   'quote.currency': {'newname': 'QuoteCurrency', 'drop': True},
                                   'quote.pricedElements': {'newname': 'PricedElements', 'drop': False},
                                   'quote.paymentFrequency': {'newname': 'QuotePaymentFrequency', 'drop': False},
                                   'quote.paymentFrequencyOptions': {'newname': 'paymentFrequencyOptions',
                                                                     'drop': True},
                                   'quote.totalPremiums.finalPremiumsBeforeTaxesRounded._12M': {
                                       'newname': 'TotPremFPBFTX_12M', 'drop': True},
                                   'quote.totalPremiums.finalPremiumsBeforeTaxesRounded.forSelectedPaymentFrequency': {
                                       'newname': 'TotPremFPBFTX_SPF', 'drop': True},
                                   'quote.totalPremiums.finalPremiumsBeforeTaxesRounded.forTotalPeriod': {
                                       'newname': 'TotPremFPBFTX_TP', 'drop': False},
                                   'quote.totalPremiums.taxesRounded._12M': {'newname': 'TotPremTX_12M', 'drop': True},
                                   'quote.totalPremiums.taxesRounded.forSelectedPaymentFrequency': {
                                       'newname': 'TotPremTX_SPF', 'drop': True},
                                   'quote.totalPremiums.finalPremiums._12M': {'newname': 'TotPremFPAFTX_12M',
                                                                              'drop': True},
                                   'quote.totalPremiums.finalPremiums.forSelectedPaymentFrequency': {
                                       'newname': 'TotPremFPAFTX_SPF', 'drop': True},
                                   'quote.totalPremiums.finalPremiums.forTotalPeriod': {'newname': 'TotPremFPAFTX_TP',
                                                                                        'drop': False},
                                   'clientError.violations': {'newname': 'ClientError', 'drop': False},
                                   'serverError.traceId': {'newname': 'ServerError', 'drop': False}}


outputcolumn_renamer = {'premiums_finalPremiums__12M': 'EndCustPrem', 'premiums_finalPremiumsBeforeTaxes__12M': 'GWP',
                        'premiums_finalPremiumsBeforeTaxes_forSelectedPaymentFrequency': 'GWP_SelPayFreq',
                        'premiums_finalPremiumsBeforeTaxes_forTotalPeriod': 'GWP_TotPer',
                        'premiums_finalPremiums_forSelectedPaymentFrequency': 'EndCustPrem_SelPayFreq',
                        'premiums_finalPremiums_forTotalPeriod': 'EndCustPrem_TotPer'}

columns_for_finalpremium_rescaling = [
    'premiums_finalPremiums_forSelectedPaymentFrequency',
    'premiums_finalPremiums_forTotalPeriod']


def printtestsummary(success_report):
    print('Test Report:')
    for x, profiles in success_report.items():
        print(''.join([x, ': ', '{:4d}'.format(len(profiles['SuccessProfiles'])), ' successful profiles.']))
        print(''.join([x, ': ', '{:4d}'.format(len(profiles['FailedProfiles'])), ' failed profiles.']))
        if len(profiles['FailedProfiles']) > 0:
            print(''.join(['Failed profiles: ', ','.join(profiles['FailedProfiles'])]))


def writefulloutput(filename, output_final, success_report):
    with pd.ExcelWriter(filename) as writer:
        output_final.to_excel(writer, sheet_name='FullResult')
        pd.DataFrame.from_dict(success_report, orient='index').to_excel(writer, sheet_name='Summary')


def normalize_json_response(r):
    normalized_json = pd.json_normalize(r.json(), 'quotes')
    normalized_json.set_index('referenceId', inplace=True)

    # Assign clientError.violations column if it does not exist
    if 'clientError.violations' not in normalized_json:
        normalized_json['clientError.violations'] = None

    # Assign serverError.message column if it does not exist
    if 'serverError.message' not in normalized_json:
        normalized_json['serverError.traceId'] = None

    normalized_json = normalized_json[
        [x for x, y in TuringResponseTopLevelStructure.items() if y['drop'] is False]].rename(
        columns={x: y['newname'] for x, y in TuringResponseTopLevelStructure.items() if y['drop'] is False},
        inplace=False)
    return normalized_json


def create_final_output_df(output_tbl, indicator):
    # Intermediate result: Quote and Tariffinfo
    metadata_table = output_tbl.loc[:, (slice(None), 'INFO')]
    metadata_table.columns = metadata_table.columns.get_level_values(0)

    # Process Full Output Table for Downstream Use
    output_tbl = output_tbl.reorder_levels([0, 2, 3, 1], axis=1)
    output_prem = output_tbl.loc[:, ('PREMIUMINFO', slice(None))].droplevel(0, axis=1)
    indicator.columns = pd.MultiIndex.from_tuples(
        [(*[str(y) for y in x],) for x in indicator.columns])  # Convert object id to string
    indicator.sort_index(axis=1, inplace=True)
    indicator_broad = pd.DataFrame(columns=output_prem.columns, index=indicator.index)

    for x in output_prem.columns:
        indicator_broad[x] = indicator.loc[:, (x[0], indicator.columns.isin(x[:2], level=1))].squeeze()

    output_prem = output_prem.multiply(indicator_broad)
    output_prem.columns = pd.MultiIndex.from_tuples(
        [x + indicator.loc[:, (*x[0:2],)].columns[0] for x in output_prem.columns])

    # iterate over number of aggregation levels
    aggr_output_prem = pd.DataFrame()
    for ctr in range(4, output_prem.columns.nlevels):
        tmp = output_prem.T.groupby(level=[2, ctr]).sum().T
        tmp.columns = pd.MultiIndex.from_tuples([(tuple(['AGG', str(ctr - 4)]) + (*x,)) for x in tmp.columns])
        aggr_output_prem = pd.concat([aggr_output_prem, tmp], axis=1)

    # After multiplication and aggregation, convert to nan to avoid wrong averaging along profile dimension
    aggr_output_prem = aggr_output_prem.replace({0: np.nan})

    # Rename some columns for better readability
    aggr_output_prem.columns = pd.MultiIndex.from_tuples(
        [tuple([outputcolumn_renamer.get(x, x) for x in y]) for y in aggr_output_prem.columns])

    # Flatten MultiIndex
    aggr_output_prem.columns = ['-'.join(x) for x in aggr_output_prem.columns]

    return aggr_output_prem.merge(metadata_table, left_index=True, right_index=True)


def process_row_out(rowdata, row, pricedEl, includeQuoteDetailsForOutputMapping, includeAllQuoteDetailsForOutputMapping,
                    pricedElementsConsideredForOutputMapping, product_id):
    pricedElements = mappers_global.getPricedElements(product_id)
    rowdata = pd.DataFrame(rowdata).T
    rowdata.columns = pd.MultiIndex.from_tuples([(x, 'INFO', 'INFO', 'INFO') for x in rowdata.columns])

    l2 = [
        a[['MainObject', 'id', 'Row', 'quoteDetails'] + [x for x in a.columns if ('premiums_finalPremiums' in x) & (
                'Rounded' not in x)]].set_index(['MainObject', 'id', 'Row'], inplace=False)
        for a in [
            pd.DataFrame([{**flatten(z), **{'MainObject': w}, **{'Row': row}} for z in
                          {x['id']: x['coverages'] for x in pricedEl if
                           int(x['id']) in list(pricedElements.values())}[w]])
            for w in
            {x['id']: x['coverages'] for x in pricedEl if int(x['id']) in list(pricedElements.values())}
        ]
    ]

    # Divide final premiums by 100 as per Turing convention
    for tmp in l2:
        tmp[columns_for_finalpremium_rescaling] = tmp[columns_for_finalpremium_rescaling].div(100)

    if includeQuoteDetailsForOutputMapping:
        quotD = pd.DataFrame()
        for ctr in range(0, len(l2)):
            tmp_peril = pd.DataFrame()
            for ctr_peril in range(0, len(l2[ctr].index)):
                if (l2[ctr]['quoteDetails'].iloc[ctr_peril] != []):

                    if includeAllQuoteDetailsForOutputMapping == False:
                        tmp = pd.json_normalize([x for x in l2[ctr]['quoteDetails'].iloc[ctr_peril] if
                                                 x['id'] in pricedElementsConsideredForOutputMapping]).set_index(
                            'id')[['value']].T
                    else:
                        tmp = \
                            pd.json_normalize([x for x in l2[ctr]['quoteDetails'].iloc[ctr_peril]]).set_index('id')[
                                ['value']].T
                    tmp.index = l2[ctr].index[[ctr_peril]]
                else:
                    tmp = pd.DataFrame()
                tmp_peril = pd.concat([tmp_peril, tmp], ignore_index=False)
            quotD = pd.concat([quotD, tmp_peril])

        l2 = pd.concat(*[l2]).drop('quoteDetails', axis=1).merge(quotD, left_index=True, right_index=True,
                                                                 how='left').unstack(level=[0, 1])
    else:
        l2 = pd.concat(*[l2]).drop('quoteDetails', axis=1).unstack(level=[0, 1])

    l2.columns = pd.MultiIndex.from_tuples([('PREMIUMINFO', *x) for x in l2.columns.to_list()])

    return pd.concat([rowdata, l2], axis=1)


def create_output_tbl(normalized_json, includeQuoteDetailsForOutputMapping, includeAllQuoteDetailsForOutputMapping,
                      pricedElementsConsideredForOutputMapping, product_id):
    output_tbl = []

    for row, rowdata in normalized_json.iterrows():
        if rowdata['ProductId'] is not np.nan:
            pricedEl = rowdata.pop('PricedElements')
            processed_row = process_row_out(rowdata, row, pricedEl, includeQuoteDetailsForOutputMapping,
                                            includeAllQuoteDetailsForOutputMapping,
                                            pricedElementsConsideredForOutputMapping, product_id)
            output_tbl.append(processed_row)
    return pd.concat(output_tbl, axis=0)


def extract_configuration_settings(outputmappingConfiguration):
    return (
        outputmappingConfiguration['includeQuoteDetailsForOutputMapping'],
        outputmappingConfiguration['includeAllQuoteDetailsForOutputMapping'],
        outputmappingConfiguration['pricedElementsConsideredForOutputMapping'],
        outputmappingConfiguration['productname']
    )


def flatten(d, parent_key='', sep='_'):
    items = []
    for k, v in d.items():
        new_key = parent_key + sep + k if parent_key else k
        if isinstance(v, abc.MutableMapping):
            items.extend(flatten(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)


@execution_time_decorator
def mapper_TuringOutputPremia(r, indicator, outputmappingConfiguration):
    includeQuoteDetailsForOutputMapping, includeAllQuoteDetailsForOutputMapping, pricedElementsConsideredForOutputMapping, product_id = extract_configuration_settings(
        outputmappingConfiguration)
    normalized_json = normalize_json_response(r)
    output_tbl = create_output_tbl(normalized_json, includeQuoteDetailsForOutputMapping,
                                   includeAllQuoteDetailsForOutputMapping, pricedElementsConsideredForOutputMapping,
                                   product_id)
    ErrorTable = normalized_json[['ClientError', 'ServerError']].assign(
        comparison=(pd.notna((normalized_json['ClientError']))) | (pd.notna(normalized_json['ServerError']))).apply(
        lambda x: x[['ClientError', 'ServerError']].to_dict() if x['comparison'] is True else np.nan, axis=1)
    ErrorTable.name = 'Errors'
    output_tbl = output_tbl.reindex(ErrorTable.index)
    output_tbl.update({('ClientError', 'INFO', 'INFO', 'INFO'): ErrorTable})

    final_output_df = create_final_output_df(output_tbl, indicator)

    return final_output_df
