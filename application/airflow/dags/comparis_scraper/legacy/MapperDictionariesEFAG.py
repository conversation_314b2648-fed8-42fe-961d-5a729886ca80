def getMapperDict(Source, Target):

    # The keys are defined such that the concatenated string "Source+Target" from the input parameters points to the right dict element
    Mappers = {
        'PortfolioPricedElements': {
            'Deductible_PMOD': 'deductible_mod',
            'Deductible_Collision': 'deductible_mod_collision',
            'Deductible_MTPL': 'deductible_mtpl',
            'MTPL_is_selected': ('PerilIsInsured', 'MTPL', 'P0064'),
            'MOD_is_selected': ('PerilIsInsured', 'MOD Object', 'P0045'),
            'Collision_is_selected': ('PerilIsInsured', 'MOD Object', 'P0075'),
            'ParkedVehicles_is_selected': ('PerilIsInsured', 'MOD Object', 'P0084'),
            'GlassExtension_is_selected': ('PerilIsInsured', 'MOD Glass plus', 'P0072'),
            'SpecialExpenses_is_selected': ('PerilIsInsured', 'MOD Object', 'BE001'),
            'PersonalEffects_is_selected': ('PerilIsInsured', 'MOD Object', 'BE002'),
            'GrossNegligence_is_selected': ('PerilIsInsured', 'MOD Object', 'DB001'),
            'SpecialExpenses_sumInsured': ('insured_value_be', 'MOD Object', 'BE001'),
            'PersonalEffects_sumInsured': ('insured_value_be', 'MOD Object', 'BE002'),
            'PurchasePriceInd_is_selected': ('PerilIsInsured', 'MOD Object', 'DB005')
        },
        'PortfolioRiskQuestions': {
            'HOLDER_IS_EMPLOYEE': 'holder_is_employee',
            'DRV_RESIDENCE_PERMIT': 'drv_residence_permit',
            'MANUFACTURE_DATE': 'manufacture_date',
            'VEHICLE_TYPE': 'vehicle_type',
            'VEH_POWER': 'veh_power',
            'VEH_MODEL': 'VEH_MODEL',
            'MODEL_TYPE': 'model_type',
            'ACC_CATALOG_PRICE': 'acc_catalog_price',
            'VEH_OWNER_IS_HOLDER': 'veh_owner_is_holder',
            'DRV_NATIONALITY': 'drv_nationality',
            'POLICY_START': 'policy_start', #TODO: this should be parameterized
            'INTENDED_USE': 'intended_use',
            'VEH_EMPTY_WEIGHT': 'VEH_EMPTY_WEIGHT',
            'VEH_TOTAL_WEIGHT': 'veh_total_weight',
            'GROSS_NEGLIGENCE': 'gross_negligence',
            'DRIVERS_17_25': 'drivers_17_25',
            'VEH_LEASING': 'veh_leasing',
            'DRV_LICENSE_DATE': 'drv_license_date',
            'DRV_ZIP_CODE': 'post_code',
            'MILEAGE': 'mileage',
            'USUAL_PARK_LOCATION': 'usual_park_location',
            'PAYMENT_FREQUENCY': 'payment_frequency',
            'MAIN_DRV_IS_HOLDER': 'main_drv_is_holder',
            'DRV_DOB': 'drv_dob',
            'MATRICULATION': 'matriculation',
            'DISPLACEMENT': 'displacement',
            'ENERGY_EFFICIENCY': 'energy_efficiency',
            'VEH_CATALOG_PRICE': 'veh_catalog_price',
            'DRV_CITY': 'city',
            'DRV_GENDER': 'drv_gender',
            'GEAR_TYPE': 'gear_type',
            'VEH_BRAND': 'veh_brand',
            'VEH_SEATS': 'VEH_SEATS',
            'VEH_DOORS': 'VEH_DOORS',
            'FUEL_TYPE': 'fuel_type',
            'VEHICLE_BODY_SHAPE': 'vehicle_body_shape',
            'LEASING_DURATION': 'LEASING_DURATION'
        }
    }

    return Mappers[Source+Target]