import json
import os
import time

# import datalakeConnectorLib.DLlogin as login
import MapperEFAG as MapperEFAG
import numpy as np
import pandas as pd
import requests
import tarifftestlib

# engine = login.ConnectEngine().get_engine()

path_batch = 'https://prep.d.eu1.caramelspec.com/turing/api/v2/quote/batch'
header = {'accept': '*/*', 'Content-Type': 'application/json', 'api-key': '1rZscuHRbRGplYCLli'}
# proxyDict = {'http': 'http://localhost:9999'}
# ['BURNING_COST_WITHOUT_ROUNDING', 'ALAE', 'ULAE_EXT', 'GEN_EXP', 'PAYMENT_METHOD', 'MARGIN',
# 'EMPLOYEE_DISCOUNT', 'DISCOUNT_LOADING_STRUCTURAL', 'DISCOUNT_LOADING_FLAT', 'DISCOUNT_LOADING_RENEWAL',
# 'DISCOUNT_LOADING_START_CHANGE', 'ROUND_INSTALLMENT', 'REINSURANCE', 'COMMISSION',
# 'ACCIDENT_PREVENTION_CONTRIBUTION', 'COUNTRY_TAX', 'NBI', 'NGF']

productname = 'CHMOTOR'
inputfilename = 'ComparisEFAG'
indexColumn = 'CrawlerID'
project_name = 'Project Matricul 2023'
experiment_name = 'November25'
aggregation_file_path = '/Users/<USER>/repos/price-crawler/application/airflow/dags/input_parameters/comparis/config/ObjectPerilStructureEFAG.xlsx'
basepath = os.path.join(
    '/Users/<USER>/repos/EFAG/Tariff Development/Webcrawling/Data',
    project_name, 'Experiments', experiment_name)

# Output Mapping Config: Include premium components besides the final premia
outputmappingConfiguration = {'includeQuoteDetailsForOutputMapping': False,
                              'includeAllQuoteDetailsForOutputMapping': False,
                              'pricedElementsConsideredForOutputMapping': ['ALAE', 'BURNING_COST_WITHOUT_ROUNDING'],
                              'aggregation_file_path': aggregation_file_path,
                              'aggregation_sheet_name': 'relations',
                              'productname': 'CHMOTOR',
                              'productVersion': 3,
                              'pricingVersion': '27'
                              }

input = pd.read_csv(os.path.join(basepath, 'Prescribed Input Data', '.'.join([inputfilename, 'csv'])), dtype=str)
input.set_index(indexColumn, inplace=True)
input['LEASING_DURATION'] = 0
input = input.rename(columns = {'What is the vehicles displacement (Cubic capacity)?':'DISPLACEMENT'})

tic = time.time()
output_dict, indicator = MapperEFAG.mapper_RL2Turing(input, outputmappingConfiguration['pricingVersion'],
                                                     outputmappingConfiguration['productVersion'],
                                                     outputmappingConfiguration['productname'],
                                                     outputmappingConfiguration['aggregation_file_path'],
                                                     outputmappingConfiguration['aggregation_sheet_name'])
print('execution time mapper_RL2Turing: ' + str(time.time() - tic))

file_path = "output.json"
with open(file_path, "w") as json_file:
    json.dump(output_dict, json_file, indent=4)
request_data = json.dumps(output_dict)
tic = time.time()
r = requests.post(path_batch, data=request_data, headers=header)
print('roundtrip time: ' + str(time.time() - tic))
print("response status: " + str(r.status_code))
output = tarifftestlib.mapper_TuringOutputPremia(r, indicator, outputmappingConfiguration)

output_final = output.merge(input, left_index=True, right_index=True, how='right')
output_final['iptiQPriceVersion'] = outputmappingConfiguration['pricingVersion']
output_final.insert(0, 'ExperimentName', experiment_name)
output_final.insert(0, 'ProjectName', project_name)
output_final.index.set_names('CrawlerID', inplace=True)

output_final.reset_index().to_csv(
    os.path.join(basepath, 'Turing Result', ''.join([inputfilename, 'PriceV ', outputmappingConfiguration['pricingVersion'], '.csv'])),
    index=False)

# output_final.to_sql('test_max', engine, schema='sandbox_actuaries', if_exists='replace', index=False, method='multi', chunksize=1000)
