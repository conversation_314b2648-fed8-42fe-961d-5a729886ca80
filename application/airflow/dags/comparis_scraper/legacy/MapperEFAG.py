import datetime
from collections import abc

import numpy as np
import pandas as pd

import MapperDictionariesEFAG as MapperDictionariesEFAG

# pd.set_option('future.no_silent_downcasting', True) # Remove once pandas 3.0 is in use

# Structure of paylod:
# level 1: ['quoteRequests']: eine list []
# level 2: dict: ['referenceId', 'request']
# referenceId: str
# request: dict: ['product', 'pricingVersion', 'answers', 'operationType', 'volatile', 'pricedElements']
# product: dict {'id': 'CHMOTOR', 'version': '1'}
# pricingVersion: str
# answers: dict, keys are keys of Mapper_BasketColumns2riskQuestions_EFAG
# operationType: str 'FIRST'
# volatile: bool True or False
# pricedElements: list of dicts. every dict is associated with an object id (4 for EFAG)

riskQuestions_EFAG = ['HOLDER_IS_EMPLOYEE', 'VEHICLE_TYPE', 'VEH_POWER', 'VEH_MODEL', 'MODEL_TYPE',
                      'ACC_CATALOG_PRICE',
                      'VEH_OWNER_IS_HOLDER', 'DRV_NATIONALITY', 'POLICY_START', 'INTENDED_USE', 'VEH_EMPTY_WEIGHT',
                      'VEH_TOTAL_WEIGHT', 'GROSS_NEGLIGENCE',
                      'DRIVERS_17_25', 'VEH_LEASING', 'DRV_LICENSE_DATE', 'DRV_ZIP_CODE', 'MILEAGE',
                      'USUAL_PARK_LOCATION',
                      'MAIN_DRV_IS_HOLDER', 'DRV_DOB', 'MATRICULATION', 'DISPLACEMENT', 'ENERGY_EFFICIENCY',
                      'VEH_CATALOG_PRICE', 'DRV_CITY', 'DRV_GENDER',
                      'GEAR_TYPE', 'VEH_BRAND', 'VEH_SEATS', 'VEH_DOORS', 'FUEL_TYPE', 'VEHICLE_BODY_SHAPE',
                      'LEASING_DURATION', 'LEASING_START', 'LEASING_END']
dummy_vehicle_EFAG = {'VEHICLE_TYPE': 'PASSENGER_CAR', 'VEH_MODEL': 'NX',
                      'MODEL_TYPE': 'LEXUS NX',
                      'ENERGY_EFFICIENCY': 'G', 'GEAR_TYPE': 'CONTINUOUS', 'VEH_BRAND': 'Lexus', 'VEH_SEATS': 5,
                      'VEH_DOORS': 5,
                      'VEHICLE_BODY_SHAPE': 'SEDAN', 'VEH_EMPTY_WEIGHT': 1000,
                      'USUAL_PARK_LOCATION': 'YES'}
dummy_variables_other_EFAG = {'POLICY_START': (datetime.date.today() + datetime.timedelta(2)).strftime('%d/%m/%Y'),
                              'VEH_OWNER_IS_HOLDER': 'YES', 'MAIN_DRV_IS_HOLDER': 'YES'
                              }

# Declare stuff
specExpIds_core = ['BE057', 'BE001', 'BE007', 'BE051', 'BE050', 'BE012']
locks_and_disposal = ['BE056', 'BE008']

outputcolumn_renamer = {'premiums_finalPremiumsBeforeTaxes_forSelectedPaymentFrequency': 'GWP_SelectedPaymentFrequency',
                        'premiums_finalPremiumsBeforeTaxes_forTotalPeriod': 'GWP_TotalPeriod',
                        'premiums_finalPremiums_forSelectedPaymentFrequency': 'EndCustPrem_SelectedPaymentFrequency',
                        'premiums_finalPremiums_forTotalPeriod': 'EndCustPrem_TotalPeriod'}


def make_MODAddOns_EFAG(x, prel_id):
    global specExpIds_core
    global locks_and_disposal

    if prel_id == '408':
        specExpIds = specExpIds_core + locks_and_disposal
    else:
        specExpIds = specExpIds_core

    PersEffId = 'BE002'
    GlassPlus = []

    PersEff = [{'id': PersEffId,
                'selected': x['PersonalEffects_is_selected'],
                'deductible': {
                    'options': None,
                    'value': 0
                },
                'sumInsured': {'value': x['PersonalEffects_sumInsured'] * 100 if x[
                                                                                     'PersonalEffects_is_selected'] == True else None}
                }]

    specExp = [{'id': a,
                'selected': x['SpecialExpenses_is_selected'],
                'deductible': {
                    'options': None,
                    'value': 0
                },
                'sumInsured': {'value': x['SpecialExpenses_sumInsured'] * 100 if x[
                                                                                     'SpecialExpenses_is_selected'] == True else None}}
               for a in specExpIds
               ]

    if prel_id == '410':
        GlassPlus = [{'id': 'P0072',
                      'selected': x['GlassExtension_is_selected']}]

    return specExp + PersEff + GlassPlus


def make_GrossNegligence(x):
    vl = None
    if x['GROSS_NEGLIGENCE'] == 'YES':
        vl = True
        ded = 0
    elif x['GROSS_NEGLIGENCE'] == 'NO':
        vl = False
        ded = None
    return [{'id': 'DB001',
             'deductible': {
                 'value': ded
             },
             'selected': vl
             }]


def map_boolean_columns_EFAG(output_ra, columns):
    output_ra[columns] = output_ra[columns].replace({'Yes': True, 'No': False})


def cast_numeric_columns_EFAG(output_ra):
    columns = ['Deductible_PMOD',
               'Deductible_Collision',
               'Deductible_MTPL',
               'ACC_CATALOG_PRICE',
               'VEH_EMPTY_WEIGHT',
               'VEH_TOTAL_WEIGHT',
               'DISPLACEMENT',
               'VEH_POWER',
               'VEH_CATALOG_PRICE',
               'SpecialExpenses_sumInsured',
               'PersonalEffects_sumInsured',
               'LEASING_DURATION'
               ]

    output_ra[['Deductible_PMOD', 'Deductible_Collision', 'Deductible_MTPL', 'SpecialExpenses_sumInsured',
               'PersonalEffects_sumInsured', 'VEH_EMPTY_WEIGHT', 'VEH_TOTAL_WEIGHT', 'VEH_POWER', 'DISPLACEMENT',
               ]] = \
        output_ra.loc[:, ['Deductible_PMOD', 'Deductible_Collision', 'Deductible_MTPL', 'SpecialExpenses_sumInsured',
                          'PersonalEffects_sumInsured', 'VEH_EMPTY_WEIGHT', 'VEH_TOTAL_WEIGHT', 'VEH_POWER',
                          'DISPLACEMENT']].astype(float).astype(int)
    output_ra[['ACC_CATALOG_PRICE', 'VEH_CATALOG_PRICE', 'LEASING_DURATION']] = \
        output_ra.loc[:, ['ACC_CATALOG_PRICE', 'VEH_CATALOG_PRICE', 'LEASING_DURATION']].astype(
            float)


def map_string_columns_EFAG(output_ra, productversion):
    output_ra['GROSS_NEGLIGENCE'] = output_ra['GROSS_NEGLIGENCE'].str.upper()
    output_ra['HOLDER_IS_EMPLOYEE'] = output_ra['HOLDER_IS_EMPLOYEE'].str.upper()
    output_ra['VEH_OWNER_IS_HOLDER'] = output_ra['VEH_OWNER_IS_HOLDER'].str.upper()
    output_ra['USUAL_PARK_LOCATION'] = output_ra['USUAL_PARK_LOCATION'].str.upper()
    output_ra['MAIN_DRV_IS_HOLDER'] = output_ra['MAIN_DRV_IS_HOLDER'].str.upper()

    output_ra['DRIVERS_17_25'] = output_ra['DRIVERS_17_25'].map(
        {'0': 'NO', '1': 'YES', '2': 'YES', '3P': 'YES'}) if productversion in ['CHMOTOR_COMPANIES',
                                                                                'CHMOTOR_COMPANIES_LEASING'] else \
    output_ra['DRIVERS_17_25']
    output_ra['FUEL_TYPE'] = output_ra['DISPLACEMENT'].fillna(0).apply(lambda x: 'ELECTRIC' if x == 0 else 'PETROL_CAT')


def flatten(d, parent_key='', sep='_'):
    items = []
    for k, v in d.items():
        new_key = parent_key + sep + k if parent_key else k
        if isinstance(v, abc.MutableMapping):
            items.extend(flatten(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)


def calculate_AgeVariables(input):
    # Calculate Dates from Age Variables
    reference_date = datetime.date.today()
    birthday_before_reference_indays = 2

    # Calculate Age of Person
    tmp = (reference_date - datetime.timedelta(birthday_before_reference_indays))
    input['What is the main driver date of birth?'] = '/'.join(
        ['{:02d}'.format(tmp.day), '{:02d}'.format(tmp.month)]) + '/' + (tmp.year - input["DRVAGE"].astype(int)).astype(
        str)

    # Calculate Age of Driving License
    input['When has the main driver obtained the driving license (date)?'] = '/'.join(
        ['{:02d}'.format(tmp.day), '{:02d}'.format(tmp.month)]) + '/' + (tmp.year - input['AGEDRVLICENSE'].astype(
        int)).astype(str)

    # Calculate Age of Car since first matriculation
    input['When is the 1st matriculation of the vehicle on the market?'] = '/'.join(
        ['{:02d}'.format(tmp.day), '{:02d}'.format(tmp.month)]) + '/' + (tmp.year - input[
        "What's the time in years since the 1st matriculation date on the market?"].astype(int)).astype(str)

    # Calculate leasing start and end date
    input['LEASING_START'] = reference_date.strftime('%d/%m/%Y')
    input['LEASING_END'] = input['LEASING_DURATION'].fillna(0).astype(int).apply(
        lambda months: reference_date + pd.DateOffset(months=months)).dt.strftime('%d/%m/%Y')


def mapper_RL2Turing(input0, pricingVersion, productVersion, productname, aggregationFilePath, aggregation_sheet_name):
    input = pd.concat([input0, pd.DataFrame(dummy_vehicle_EFAG, index=input0.index),
                       pd.DataFrame(dummy_variables_other_EFAG, index=input0.index)], axis=1)

    calculate_AgeVariables(input)

    Mapper_BasketColumns2riskQuestions_EFAG = {
        'HOLDER_IS_EMPLOYEE': 'HOLDER_IS_EMPLOYEE',
        'DRV_RESIDENCE_PERMIT': 'MD Residence Type',
        'VEHICLE_TYPE': 'VEHICLE_TYPE',
        'VEH_POWER': 'What is the vehicles power (in kw)?',
        'VEH_MODEL': 'VEH_MODEL',
        'MODEL_TYPE': 'MODEL_TYPE',
        'ACC_CATALOG_PRICE': 'What is the catalog price of the vehicle accessories?',
        'VEH_OWNER_IS_HOLDER': 'VEH_OWNER_IS_HOLDER',
        'DRV_NATIONALITY': 'MD Nation',
        'POLICY_START': 'POLICY_START',
        'INTENDED_USE': 'What is the intended use of the vehicle?',
        'VEH_EMPTY_WEIGHT': 'VEH_EMPTY_WEIGHT',
        'VEH_TOTAL_WEIGHT': 'What is the vehicles total weight (in kg)?',
        'GROSS_NEGLIGENCE': 'GrossNegligence',
        'DRIVERS_17_25': 'How many drivers between 17 years and 25 years are in the same household with the main driver?',
        'VEH_LEASING': 'How is the vehicle purchased?',
        'DRV_LICENSE_DATE': 'When has the main driver obtained the driving license (date)?',
        'DRV_ZIP_CODE': "What's the ZIP code of the main driver's residence?",
        'MILEAGE': 'Annual Mileage in kilometres',
        'USUAL_PARK_LOCATION': 'USUAL_PARK_LOCATION',
        'PAYMENT_FREQUENCY': 'PAYMENT_FREQUENCY',
        'MAIN_DRV_IS_HOLDER': 'MAIN_DRV_IS_HOLDER',
        'DRV_DOB': 'What is the main driver date of birth?',
        'MATRICULATION': 'When is the 1st matriculation of the vehicle on the market?',
        'DISPLACEMENT': 'DISPLACEMENT',
        'ENERGY_EFFICIENCY': 'ENERGY_EFFICIENCY',
        'VEH_CATALOG_PRICE': 'What is the catalog price of the vehicle?',
        'DRV_CITY': "What is the city of the main driver's residence?",
        'DRV_GENDER': 'What is the main driver gender?',
        'GEAR_TYPE': 'GEAR_TYPE',
        'VEH_BRAND': 'VEH_BRAND',
        'VEH_SEATS': 'VEH_SEATS',
        'VEH_DOORS': 'VEH_DOORS',
        'FUEL_TYPE': 'FUEL_TYPE',
        'VEHICLE_BODY_SHAPE': 'VEHICLE_BODY_SHAPE',
        'LEASING_DURATION': 'LEASING_DURATION',
        'LEASING_START': 'LEASING_START',
        'LEASING_END': 'LEASING_END'
    }

    Mapper_BasketColumns2PricedElements_EFAG = {
        'Deductible_PMOD': 'What is the Deductible for Partial MOD?',
        'Deductible_Collision': 'What is the Deductible for Collision',
        'Deductible_MTPL': 'What is the Deductible for MTPL',
        'MTPL_is_selected': 'MTPL',
        'MOD_is_selected': 'MOD',
        'Collision_is_selected': 'Collision',
        'ParkedVehicles_is_selected': 'ParkedVehicles',
        'GlassExtension_is_selected': 'GlassExtension',
        'SpecialExpenses_is_selected': 'SpecialExpenses',
        'PersonalEffects_is_selected': 'PersonalEffects',
        'GrossNegligence_is_selected': 'GrossNegligence',
        'SpecialExpenses_sumInsured': 'SpecialExpensesSI',
        'PersonalEffects_sumInsured': 'PersonalEffSI',
        'PurchasePriceInd_is_selected': 'PurchasePriceIndemnification'
    }

    # Write Output Table for Risk Answer component of request
    output_ra = pd.DataFrame()
    [output_ra.insert(0, y, input[x]) for y, x in Mapper_BasketColumns2riskQuestions_EFAG.items()]

    # Add Elements for Priced Elements to Output Table. Subset later on columns
    [output_ra.insert(0, y, input[x]) for y, x in Mapper_BasketColumns2PricedElements_EFAG.items()]
    print(output_ra.columns)
    # Map Column values
    map_boolean_columns_EFAG(output_ra, ['MTPL_is_selected',
                                         'MOD_is_selected',
                                         'Collision_is_selected',
                                         'ParkedVehicles_is_selected',
                                         'GlassExtension_is_selected',
                                         'SpecialExpenses_is_selected',
                                         'PersonalEffects_is_selected',
                                         'PurchasePriceInd_is_selected'
                                         ])

    # Map and cast string and numeric columns
    cast_numeric_columns_EFAG(output_ra)
    map_string_columns_EFAG(output_ra, productname)

    # Rename Input column names
    renamer_input = Mapper_BasketColumns2riskQuestions_EFAG.copy()
    renamer_input.update(Mapper_BasketColumns2PricedElements_EFAG)
    renamer_input = {y: x for x, y in renamer_input.items()}
    input0.rename(columns=renamer_input, inplace=True)

    return convertTableToJSON_EFAG(output_ra, aggregationFilePath, aggregation_sheet_name,
                                   Mapper_BasketColumns2riskQuestions_EFAG, productVersion, pricingVersion, productname)


def convertTableToJSON_EFAG(output_ra, aggregationFilePath, aggregation_sheet_name, Mapper_RiskQuestions_EFAG,
                            productVersion, pricingVersion, productname):
    # Key Step: Create the Turing-readable JSON format for the /quote/batch request.
    # This conversion is product-specific and not generalizable.
    # The idea is that the input comes in rows of a pandas table and iterates through the rows.
    quote_requests_base = []
    indicator = createSelectionIndicatorTableEFAG(aggregationFilePath, aggregation_sheet_name)
    rowdata_selector_ra_base = list(Mapper_RiskQuestions_EFAG.keys())

    output_ra.apply(process_row, axis=1, args=(
    indicator, rowdata_selector_ra_base, quote_requests_base, productVersion, pricingVersion, productname))
    return {'quoteRequests': quote_requests_base}, indicator


def process_row(rowdata, indicator, rowdata_selector_ra_base, quote_requests_base, productVersion, pricingVersion,
                productname):
    # DRV_RESIDENCE_PERMIT must be dropped from answers if DRV_NATIONALITY is not in ['CH','FL']
    rowdata_selector_ra = rowdata_selector_ra_base.copy()

    if rowdata['DRV_NATIONALITY'] in ['CH', 'FL']:
        rowdata_selector_ra.remove('DRV_RESIDENCE_PERMIT')

    # Displacement Risk answer must not be submitted if primary question does not require it
    if rowdata['FUEL_TYPE'] == 'ELECTRIC':
        rowdata_selector_ra.remove('DISPLACEMENT')

    # if productname is chmotor_companies or chmotor, drop the risk question LEASING_DURATION, LEASING_START, LEASING_END
    if ((productname == 'CHMOTOR_COMPANIES') | (productname == 'CHMOTOR')):
        rowdata_selector_ra.remove('LEASING_DURATION')
        rowdata_selector_ra.remove('LEASING_START')
        rowdata_selector_ra.remove('LEASING_END')

    # drop risk question payment_frequency for all engines, it's part of the request, not the risk answers
    rowdata_selector_ra.remove('PAYMENT_FREQUENCY')

    # drop manufacture date for leasing bundle engines
    # rowdata_selector_ra.remove('MANUFACTURE_DATE')

    # if product name is CHMOTOR_COMPANIES or CHMOTOR_COMPANIES_LEASING, remove risk questions
    if ((productname == 'CHMOTOR_COMPANIES') | (productname == 'CHMOTOR_COMPANIES_LEASING')):
        rowdata_selector_ra.remove('HOLDER_IS_EMPLOYEE')
        rowdata_selector_ra.remove('VEH_OWNER_IS_HOLDER')
        rowdata_selector_ra.remove('MAIN_DRV_IS_HOLDER')

    # If product name is CHMOTOR_COMPANIES or CHMOTOR_COMPANIES_LEASING, rename risk question DRIVERS17_25 to DRIVERS_25
    if ((productname == 'CHMOTOR_COMPANIES') | (productname == 'CHMOTOR_COMPANIES_LEASING')):
        rowdata_selector_ra = [x if x != 'DRIVERS_17_25' else 'DRIVERS_25' for x in rowdata_selector_ra]
        rowdata.rename({'DRIVERS_17_25': 'DRIVERS_25'}, inplace=True)

    quote_requests_base.append(
        {
            'referenceId': str(rowdata.name),
            'include': ['QUOTE_DETAILS'],
            'request': {
                'product': {
                    'id': productname,
                    'version': productVersion
                },
                'pricingVersion': pricingVersion,
                'answers': rowdata.loc[rowdata_selector_ra].to_dict(),
                'operationType': 'FIRST',
                'paymentFrequency': rowdata['PAYMENT_FREQUENCY'],
                'volatile': True,
                'pricedElements': [{
                    'id': '267',
                    'coverages': make_GrossNegligence(rowdata),
                    'deductible': {
                        'value': rowdata['Deductible_MTPL'] * 100
                    },
                    'selected': rowdata['MTPL_is_selected']
                },
                    {'id': '408',
                     'coverages': [{'id': 'P0075',
                                    'selected': rowdata['Collision_is_selected'],
                                    'deductible': {'value': rowdata['Deductible_Collision'] * 100}
                                    },
                                   {'id': 'P0084',
                                    'selected': rowdata['ParkedVehicles_is_selected']
                                    },
                                   {'id': 'DB005',
                                    'selected': rowdata['PurchasePriceInd_is_selected']
                                    }
                                   ] +
                                  make_MODAddOns_EFAG(rowdata, '408') +
                                  make_GrossNegligence(rowdata),
                     'deductible': {
                         'value': rowdata['Deductible_PMOD'] * 100
                     },
                     'selected': rowdata['MOD_is_selected']
                     },
                    {'id': '409',
                     'coverages': make_MODAddOns_EFAG(rowdata, '409') + make_GrossNegligence(rowdata),
                     'deductible': {
                         'value': rowdata['Deductible_PMOD'] * 100
                     },
                     'selected': rowdata['MOD_is_selected']
                     },
                    {'id': '410',
                     'coverages': make_MODAddOns_EFAG(rowdata, '410') + make_GrossNegligence(rowdata),
                     'deductible': {
                         'value': rowdata['Deductible_PMOD'] * 100
                     },
                     'selected': rowdata['GlassExtension_is_selected']
                     }
                ]
            }
        })

    # Generate a table that indicates selected yes/no for each object, and priced element.
    # This will be used to multiply not selected priced components with 0.
    indicator.loc[rowdata.name, (267, ['P0064', 'P0026'])] = 1  # MTPL always selected
    indicator.loc[rowdata.name, (267, ['DB001'])] = 1 if rowdata['GROSS_NEGLIGENCE'] == 'YES' else 0 if rowdata[
                                                                                                            'GROSS_NEGLIGENCE'] == 'NO' else np.nan

    indicator.loc[rowdata.name, (408, ['P0045', 'P0069', 'P0046', 'P0071', 'P0047', 'P0073', 'P0074', 'P0050'])] = 1 if \
        rowdata['MOD_is_selected'] else 0
    indicator.loc[rowdata.name, (408, ['P0075'])] = 1 if rowdata['Collision_is_selected'] else 0

    indicator.loc[rowdata.name, (408, ['BE057', 'BE001', 'BE007', 'BE051', 'BE050', 'BE012', 'BE056', 'BE008'])] = 1 if \
        (rowdata['SpecialExpenses_is_selected'] & rowdata['MOD_is_selected']) else 0
    indicator.loc[rowdata.name, (409, ['BE057', 'BE001', 'BE007', 'BE051', 'BE050', 'BE012'])] = 1 if (rowdata[
                                                                                                           'SpecialExpenses_is_selected'] &
                                                                                                       rowdata[
                                                                                                           'MOD_is_selected']) else 0
    indicator.loc[rowdata.name, (410, ['BE057', 'BE001', 'BE007', 'BE051', 'BE050', 'BE012'])] = 1 if (rowdata[
                                                                                                           'SpecialExpenses_is_selected'] &
                                                                                                       rowdata[
                                                                                                           'GlassExtension_is_selected']) else 0

    indicator.loc[rowdata.name, (408, ['BE002'])] = 1 if (
                rowdata['PersonalEffects_is_selected'] & rowdata['MOD_is_selected']) else 0
    indicator.loc[rowdata.name, (409, ['BE002'])] = 1 if (
                rowdata['PersonalEffects_is_selected'] & rowdata['MOD_is_selected']) else 0
    indicator.loc[rowdata.name, (410, ['BE002'])] = 1 if (
                rowdata['PersonalEffects_is_selected'] & rowdata['GlassExtension_is_selected']) else 0

    indicator.loc[rowdata.name, (408, ['P0084'])] = 1 if rowdata['ParkedVehicles_is_selected'] else 0
    indicator.loc[rowdata.name, (408, ['DB001'])] = 1 if (
                (rowdata['GROSS_NEGLIGENCE'] == 'YES') & rowdata['MOD_is_selected']) else 0 if (
                (rowdata['GROSS_NEGLIGENCE'] == 'NO') | (rowdata['GROSS_NEGLIGENCE'] == 'YES') & (
            not rowdata['MOD_is_selected'])) else np.nan

    indicator.loc[rowdata.name, (409, ['P0072'])] = 1 if rowdata['MOD_is_selected'] else 0
    indicator.loc[rowdata.name, (409, ['DB001'])] = 1 if (
                (rowdata['GROSS_NEGLIGENCE'] == 'YES') & rowdata['MOD_is_selected']) else 0 if (
                (rowdata['GROSS_NEGLIGENCE'] == 'NO') | (rowdata['GROSS_NEGLIGENCE'] == 'YES') & (
            not rowdata['MOD_is_selected'])) else np.nan

    indicator.loc[rowdata.name, (410, ['P0072'])] = 1 if rowdata['GlassExtension_is_selected'] else 0
    indicator.loc[rowdata.name, (410, ['DB001'])] = 1 if (
                (rowdata['GROSS_NEGLIGENCE'] == 'YES') & rowdata['GlassExtension_is_selected']) else 0 if (
                (rowdata['GROSS_NEGLIGENCE'] == 'NO') | (rowdata['GROSS_NEGLIGENCE'] == 'YES') & (
            not rowdata['GlassExtension_is_selected'])) else np.nan

    indicator.loc[rowdata.name, (408, ['DB005'])] = 1 if rowdata['PurchasePriceInd_is_selected'] else 0


def createSelectionIndicatorTableEFAG(file_path, sheet_name):
    global ObjectPerilsStructureEFAG
    input = pd.read_excel(file_path, sheet_name=sheet_name)
    input.set_index(input.columns.tolist(), inplace=True)

    return input.T


def mapper_RL2Comparis(inputdata):
    # Keep in mind the 500 CHF deduction in the EFAG proposition we need to consider here for PMOD and Collision
    # bonus protection:
    # no: 1 (integer)
    # liability & casco: 2
    # liability: 3
    # casco: 4

    # comparis mappings for car usage:
    # Private: 1
    # Private & Commute: 4
    # Private & Commute & Business: 7
    # Commute: 2

    # garage
    # nein: 4
    # at home: 1
    # at the workplace: 2
    # home and at workplace: 3

    # Residency Type Mapping on Comparis
    # CH: null
    # B: 2
    # C: 3
    # L: 4

    # Gender Mapping is given by Comparis: Female = 1, Male = 2, numeric values

    mapper_deductible_pmod = {500: 0, 700: np.nan, 800: 300, 1000: 500, 1500: 1000, 2000: np.nan}
    mapper_deductible_collision = {500: np.nan, 800: np.nan, 1000: 500, 1500: 1000, 2500: 2000, 5500: 5000}
    mapper_deductible_mtpl = {0: 0, 500: 0, 1000: 0, 1500: 0, 2000: 0, 5000: 0}
    inputdata.replace({'What is the Deductible for Partial MOD?': mapper_deductible_pmod,
                       'What is the Deductible for Collision': mapper_deductible_collision,
                       'What is the Deductible for MTPL': mapper_deductible_mtpl}, inplace=True)

    inputdata['When is the 1st matriculation of the vehicle on the market?'] = pd.to_datetime(
        inputdata['When is the 1st matriculation of the vehicle on the market?'], format="%d/%m/%Y").map(
        lambda x: x.date())

    integer_columns = ['Make_cmp_id', 'Series_cmp_id', 'Model_cmp_id', 'Type_cmp_id', 'Geartype_cmp_id',
                       'What is the catalog price of the vehicle?',
                       'What is the catalog price of the vehicle accessories?',
                       # 'What is the Deductible for Collision','What is the Deductible for Partial MOD?',
                       'PersonalEffSI', 'VehicleID']
    inputdata[integer_columns] = inputdata[integer_columns].astype(float).astype(int)

    # Apply Comparis-Specific Mappings
    inputdata.replace({'What is the main driver gender?': {'F': 1, 'M': 2},
                       'What is the intended use of the vehicle?': {'PRIVATE': 1, 'PRIV_COMMUTE': 4,
                                                                    'PRIV_COMMUTE_BUS': 7},
                       'Garage Available?': {'WORK': 2, 'HOME': 1, 'HOME_WORK': 3, 'NO': 4},
                       'Annual Mileage in kilometres': {'0_5': 4000, '5_15': 10000, '15': 20000},
                       'How is the vehicle purchased?': {'CASH': False, 'LEASING': True},
                       'MD Residence Type': {'B': 2, 'C': 3, 'NOT_APPLICABLE': 0, 'OTHER': 4},
                       'MTPL': {'Yes': True, 'No': False},
                       'MOD': {'Yes': True, 'No': False},
                       'Collision': {'Yes': True, 'No': False},
                       'ParkedVehicles': {'Yes': True, 'No': False},
                       'PersonalEffects': {'Yes': True, 'No': False},
                       'GrossNegligence': {'YES': True, 'NO': False}
                       }, inplace=True)

    # Hack to set 0 to None.
    inputdata['MD Residence Type'] = inputdata['MD Residence Type'].astype(int)

    inputdata['What is the main driver date of birth?'] = pd.to_datetime(
        inputdata['What is the main driver date of birth?'], format='%d/%m/%Y').map(lambda x: x.isoformat() + '.000Z')
    inputdata['When has the main driver obtained the driving license (date)?'] = pd.to_datetime(
        inputdata['When has the main driver obtained the driving license (date)?'], format='%d/%m/%Y').map(
        lambda x: x.isoformat() + '.000Z')

    inputdata['CoverageType'] = np.select(
        [(inputdata['MOD'] == False), (inputdata['MOD'] == True) & (inputdata['Collision'] == False),
         (inputdata['MOD'] == True) & (inputdata['Collision'] == True)], [1, 2, 3], default=0)

    # Set Other Drivers Depending on Basket variable how many people 17-25 in same household?
    # inputdata['HasOtherDrivers'] = inputdata['How many drivers between 17 years and 25 years are in the same household with the main driver?'] != '0'

    # Set Drivers 17-25 to 0 because we send False for 'HasOtherDrivers'
    # inputdata['How many drivers between 17 years and 25 years are in the same household with the main driver?'] = '0'
    #
    # # Set Payment Frequency to Annual
    # inputdata['PAYMENT_FREQUENCY'] = '_12M'
    #
    # # SET EMPLOYEE DISCOUNT = NO
    # inputdata['HOLDER_IS_EMPLOYEE'] = 'NO'


def searchNearestVehicleCatalogPrice(rowdata, typeOptions):
    ref = rowdata['VEH_Type']
    imres = [
        [x + [int(ref.split(' - ')[-1].split(' Fr.')[0])]] + [abs(x[-1] - int(ref.split(' - ')[-1].split(' Fr.')[0]))]
        for x in
        [[''.join(x.split(' - ')[:-1]), int(x.split(' - ')[-1].split(' Fr.')[0])] for x in typeOptions] if
        x[0] == ''.join(ref.split(' - ')[:-1])]

    endres = [x for x in imres if x[-1] == min([x[-1] for x in imres])]

    final_string = '-'.join(ref.split('-')[:-1] + [' ' + str(endres[0][0][1]) + ' Fr.'])
    # Overwrite original vehicle catalog price with actually selected one
    rowdata['What is the catalog price of the vehicle?'] = str(endres[0][0][1])
    rowdata['VEH_Type'] = final_string

    # Selected_SI = endres[0][0][1]
    return final_string


def mapInForcePFToTuring(input, pricingVersion, productVersion, productname, aggregationFilePath,
                         aggregation_sheet_name):
    columns = ['policy_id', 'policy_no', 'distribution_partner', 'product_id', 'annex_id',
               'policy_status', 'policy_start_date', 'policy_end_date', 'policy_underwriting_date',
               'initial_sum_insured', 'total_premium', 'total_premium_paid', 'notax_premium',
               'tax_amount', 'premium_currency', 'transaction_reason', 'transaction_type',
               'transaction_date', 'renewal_indicator', 'inforce_indicator',
               'policy_start_date_renewals_new_business',
               'renewal_closed_because_original_closed_in_renewal_period', 'cover_id',
               'cover_description', 'cover_start_date', 'cover_end_date', 'insured_object_description',
               'insured_value_be', 'insured_value_mtpl', 'insured_value_mod', 'deductible_mtpl',
               'deductible_mod', 'deductible_mod_collision', 'flag_glass_plus_policy',
               'deductible_description', 'cover_premium_amount', 'tax_premium_amount',
               'fextax_premium_amount', 'product_category_agg', 'product_category', 'contract_type',
               'acc_catalog_price', 'canc_3_yrs', 'claims_3_yrs', 'claim_type', 'displacement',
               'drivers_17_25', 'drv_canton', 'drv_dob', 'drv_gender', 'drv_license_date',
               'drv_license_revoke', 'drv_nationality', 'drv_residence_permit', 'ef_package_id',
               'energy_efficiency', 'fuel_type', 'gear_type', 'gross_negligence', 'guilty',
               'holder_is_employee', 'homologation', 'intended_use', 'main_drv_is_holder',
               'manufacture_date', 'matriculation', 'mileage', 'model_type', 'payment_frequency',
               'policy_start', 'usual_park_location', 'veh_brand', 'veh_catalog_price',
               'vehicle_body_shape', 'vehicle_type', 'veh_leasing', 'veh_owner_is_holder',
               'veh_power', 'veh_total_weight', 'age', 'customer_birth_year', 'age_at_policy_start',
               'country_code', 'city', 'post_code', 'canton', 'country']
    cols_policy_level = ['policy_id', 'policy_no', 'distribution_partner', 'product_id',
                         'policy_status', 'policy_start_date', 'policy_end_date', 'policy_underwriting_date',
                         'initial_sum_insured', 'total_premium', 'notax_premium',
                         'renewal_indicator', 'inforce_indicator',
                         'insured_value_mtpl', 'insured_value_mod', 'deductible_mtpl',
                         'deductible_mod', 'deductible_mod_collision',
                         'acc_catalog_price', 'displacement',
                         'drivers_17_25', 'drv_canton', 'drv_dob', 'drv_gender', 'drv_license_date',
                         'drv_nationality', 'drv_residence_permit',
                         'energy_efficiency', 'fuel_type', 'gear_type', 'gross_negligence',
                         'holder_is_employee', 'homologation', 'intended_use', 'main_drv_is_holder',
                         'manufacture_date', 'matriculation', 'mileage', 'model_type', 'payment_frequency',
                         'usual_park_location', 'veh_brand', 'veh_catalog_price',
                         'vehicle_body_shape', 'vehicle_type', 'veh_leasing', 'veh_owner_is_holder',
                         'veh_power', 'veh_total_weight', 'age', 'customer_birth_year', 'age_at_policy_start',
                         'country_code', 'city', 'post_code', 'canton', 'country']

    Mapper_RiskQuestions_EFAG = MapperDictionariesEFAG.getMapperDict('Portfolio', 'RiskQuestions')
    Mapper_PricedElements_EFAG = MapperDictionariesEFAG.getMapperDict('Portfolio', 'PricedElements')

    input.drop(['product_category_agg'], axis=1, inplace=True)
    extract_tp = input[input.columns.intersection(
        ['policy_id', 'cover_description', 'cover_id', 'insured_object_description', 'insured_value_be'])].set_index(
        ['policy_id', 'cover_id', 'insured_object_description']).rename(
        columns={'cover_description': 'PerilIsInsured'}).unstack(level=[2, 1])
    extract_tp[('PerilIsInsured')] = extract_tp[('PerilIsInsured')].notna()
    extract_tp_pollevel = input[
        list(set(cols_policy_level + list(Mapper_RiskQuestions_EFAG.values())))].drop_duplicates()

    extract_tp_pollevel = extract_tp_pollevel.merge(extract_tp, left_on='policy_id', right_on='policy_id', how='inner',
                                                    validate='1:1')

    extract_tp_pollevel.rename(columns={x: y for y, x in Mapper_RiskQuestions_EFAG.items()}, inplace=True)
    extract_tp_pollevel.rename(columns={x: y for y, x in Mapper_PricedElements_EFAG.items()}, inplace=True)

    # Write Output Table for Risk Answer component of request
    output_ra = pd.DataFrame()
    [output_ra.insert(0, y, extract_tp_pollevel[y]) for y, x in Mapper_RiskQuestions_EFAG.items()]

    # Add Elements for Priced Elements to Output Table. Subset later on columns
    [output_ra.insert(0, y, extract_tp_pollevel[y]) for y, x in Mapper_PricedElements_EFAG.items()]

    # Fillna if certain perils are not selected
    output_ra.loc[output_ra['Collision_is_selected'] == False, 'Deductible_Collision'] = output_ra.loc[
        output_ra['Collision_is_selected'] == False, 'Deductible_Collision'].fillna(500)
    output_ra.loc[output_ra['MOD_is_selected'] == False, 'Deductible_PMOD'] = output_ra.loc[
        output_ra['MOD_is_selected'] == False, 'Deductible_PMOD'].fillna(500)
    output_ra.loc[output_ra['SpecialExpenses_is_selected'] == False, 'SpecialExpenses_sumInsured'] = output_ra.loc[
        output_ra['SpecialExpenses_is_selected'] == False, 'SpecialExpenses_sumInsured'].fillna(0)
    output_ra.loc[output_ra['PersonalEffects_is_selected'] == False, 'PersonalEffects_sumInsured'] = output_ra.loc[
        output_ra['PersonalEffects_is_selected'] == False, 'PersonalEffects_sumInsured'].fillna(0)
    output_ra.loc[:, 'PAYMENT_FREQUENCY'] = output_ra.loc[:, 'PAYMENT_FREQUENCY'].fillna('_12M')

    # Adjust Datetime format
    output_ra['MANUFACTURE_DATE'] = pd.to_datetime(output_ra['MANUFACTURE_DATE'], format='%Y-%m-%d %X%z',
                                                   errors='coerce').dt.strftime('%d/%m/%Y')
    output_ra['DRV_LICENSE_DATE'] = pd.to_datetime(output_ra['DRV_LICENSE_DATE'], format='%Y-%m-%d %X%z',
                                                   errors='coerce').dt.strftime('%d/%m/%Y')

    # Remap Levels
    output_ra['DRV_GENDER'].replace({'Female': 'F', 'Male': 'M', 'Other': 'OTHER'}, inplace=True)
    output_ra['VEH_LEASING'].replace({'Leasing (standalone)': 'LEASING', 'Cash': 'CASH'}, inplace=True)
    output_ra['DRIVERS_17_25'].replace({'>=3': '3P'}, inplace=True)
    output_ra['DRV_NATIONALITY'] = output_ra['DRV_NATIONALITY'].map(
        {'Switzerland': 'CH',
         'Germany': 'DE',
         'Kosovo': 'XK',
         'Spain': 'ES',
         'Portugal': 'PT',
         'Italy': 'IT',
         'Norway': 'NO',
         'France': 'FR',
         'Sweden': 'SE',
         'Netherlands': 'NL',
         'Belgium': 'BE',
         'Serbia': 'RS',
         'Iceland': 'IS',
         'Luxembourg': 'LU',
         'Turkey': 'TR',
         'Austria': 'AT',
         'Finland': 'FI',
         }).fillna('OTHER')

    # Remove all nan-containing rows – drop if any column of a row contains a nan
    output_ra.dropna(subset=list(output_ra.columns.difference(['DRV_RESIDENCE_PERMIT', 'PAYMENT_FREQUENCY'])),
                     inplace=True)

    # Map and cast string and numeric columns
    cast_numeric_columns_EFAG(output_ra)
    map_string_columns_EFAG(output_ra)

    # Set proper index
    output_ra['index'] = np.arange(0, len(output_ra.index)).astype(str)
    output_ra.set_index('index', inplace=True)

    output_dict, indicator = convertTableToJSON_EFAG(output_ra, aggregationFilePath, aggregation_sheet_name,
                                                     Mapper_RiskQuestions_EFAG, productVersion, pricingVersion,
                                                     productname)

    return output_dict, indicator, output_ra
