"""
Motor insurance request models for Comparis.ch scraper.

This module defines the data models used for creating and processing
motor insurance quote requests on the Comparis.ch platform. The models
provide validation, type safety, and conversion utilities between
internal data representation and the Comparis API format.
"""

from datetime import date, datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Dict, Optional

from pydantic import BaseModel, Field


class ComparisMotorScraperArguments(BaseModel):
    """Base arguments for Comparis scraper configuration.

    Defines the input parameters needed to run the scraper, including
    the source data file metadata, and processing options.
    """

    csv: Path = Field(..., description="Path to the input CSV file containing experiment parameters.")
    chunk_size: int = Field(1000, description="Number of parameters to process for each output file.")
    md5_hash: str = Field(None, description="md5 hash of the input parameters file.")


class VehicleInput(BaseModel):
    """Vehicle information model for insurance quotes.

    Contains all required vehicle details needed for generating
    an accurate motor insurance quote, including make, model,
    and usage characteristics.
    """

    matriculation_month: int
    matriculation_year: int
    make_id: int
    series_id: int
    car_model_id: int
    gear_type_id: int
    type_id: int
    equipment_price: int = Field(ge=0)
    is_leased: bool = False
    km_per_year: int = Field(ge=0)
    usage: int
    garage: int
    canton: str
    type_certificate: str


class DriverInput(BaseModel):
    """Driver information model for insurance quotes.

    Contains personal and license information about the primary
    driver, which affects insurance pricing and eligibility.
    """

    birth_date: str
    gender: int
    town_id: str
    nationality: str
    residence_permit: Optional[int]
    license_date: str


class CoverageInput(BaseModel):
    """Insurance coverage options model.

    Defines the desired insurance coverage parameters, including
    coverage type, deductibles, and additional coverage options.
    """

    coverage_type: int
    retention_partial: Optional[int]
    retention_collision: Optional[int]
    has_parking_damage: bool = False
    has_personal_effects: bool = False
    has_gross_negligence: bool = False


class InsuranceQuoteRequest(BaseModel):
    """Complete insurance quote request model.

    Combines vehicle, driver, and coverage information into a complete
    request that can be transformed into the format required by the
    Comparis API.
    """

    crawler_id: int
    vehicle: VehicleInput
    driver: DriverInput
    coverage: CoverageInput

    def to_comparis_format(self) -> Dict:
        """Transform the InsuranceQuoteRequest to the desired Comparis format."""
        # Calculate the insurance start date as three months from today
        insurance_start_date = (datetime.now() + timedelta(days=90)).strftime("%Y-%m-%dT%H:%M:%S.000Z")

        return {
            "Header": {"Language": "en"},
            "UserEmail": "",
            "OptimatisToggleState": True,
            "InsuranceStartDate": insurance_start_date,  # Use the dynamic date
            "CarInput": {
                "ImmatriculationMonth": self.vehicle.matriculation_month,
                "ImmatriculationYear": self.vehicle.matriculation_year,
                "SearchMode": 3,  # Assuming SearchMode is dynamically assigned in Request 2
                "CarMake": self.vehicle.make_id,
                "CarModelSeries": self.vehicle.series_id,
                "CarModel": self.vehicle.car_model_id,
                "CarGearType": self.vehicle.gear_type_id,
                "CarType": self.vehicle.type_id,
                "CarEquipmentPrice": self.vehicle.equipment_price,
                "YearOfPurchase": 2024,  # Retained from Request 1
                "IsLeased": self.vehicle.is_leased,
                "KmPerYear": self.vehicle.km_per_year,
                "CarUsage": self.vehicle.usage,
                "CarGarage": self.vehicle.garage,
                "CarRegistrationCanton": self.vehicle.canton,
                "CarTypeCertificate": self.vehicle.type_certificate,
                "LicenseCanton": "",  # Retained from Request 1
                "LicenseNumber": "",  # Retained from Request 1
            },
            "Driver": {
                "BirthDate": self.driver.birth_date,
                "Gender": self.driver.gender,
                "TownId": self.driver.town_id,
                "Nationality": self.driver.nationality,
                "ResidencePermit": self.driver.residence_permit,
                "IsDriverInsuranceTaker": True,  # Retained from Request 1
                "LicenseDate": self.driver.license_date,
                "IsOtherDriverUnder25": False,  # Retained from Request 1
                "CurrentProvider": 99999,  # Retained from Request 1
            },
            "Coverages": {
                "CoverageType": self.coverage.coverage_type,
                "RetentionPartialCover": self.coverage.retention_partial,
                "RetentionCollisionCascoCover": self.coverage.retention_collision,
                "HasBonusProtection": True,  # Retained from Request 1
                "HasParkingDamage": self.coverage.has_parking_damage,
                "HasParkingDamageUnlimited": True,  # Retained from Request 1
                "HasPersonalEffects": self.coverage.has_personal_effects,
                "HasPassengerAccident": False,  # Retained from Request 1
                "WantsPreselection": True,  # Retained from Request 1
                "HasGrossNegligence": self.coverage.has_gross_negligence,
                "HasAssistance": False,  # Retained from Request 1
            },
            "ClaimsAndConvictionsQuestions": {
                "HasLiabilityDamage": False,  # Retained from Request 1
                "HasCascoAndParkingDamage": False,  # Retained from Request 1
                "HasDrivingLicenseSuspension": False,  # Retained from Request 1
                "HasImprisonmentRecords": False,  # Retained from Request 1
                "HasRequestRejected": False,  # Retained from Request 1
                "HasTerminatedByInsurance": False,  # Retained from Request 1
            },
        }
