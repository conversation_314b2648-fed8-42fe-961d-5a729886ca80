import json
import logging
from typing import Dict, Any, Optional

import pandas as pd
import requests
from comparis_scraper.constants.urls import TURING_BASE_URL, XCALLER_INTERNAL
from comparis_scraper.transformers.turing_transformer import TuringTransformer

logger = logging.getLogger(__name__)


class TuringInsuranceScraper:
    """
    Synchronous scraper for Turing insurance quotes.
    
    This class handles the process of fetching insurance quotes from the Turing API,
    including data transformation, request submission, and result processing.
    """

    def __init__(
        self,
        base_url: str = TURING_BASE_URL,
        api_key: str = None,
        max_retries: int = 3,
    ):
        """Initialize the TuringInsuranceScraper."""
        self.base_url = base_url
        self.api_key = api_key
        self.max_retries = max_retries
        self.transformer = TuringTransformer()
        
    def get_headers(self) -> Dict:
        """Get request headers including API key."""
        headers = {
            'accept': '*/*', 
            'Content-Type': 'application/json',
            **XCALLER_INTERNAL
        }
        if self.api_key:
            headers['api-key'] = self.api_key
        return headers

    def process_data(self, input_data: pd.DataFrame, args: Any) -> pd.DataFrame:
        """
        Process entire dataset in a single batch request

        Args:
            input_data: DataFrame containing input data
            args: Configuration arguments

        Returns:
            DataFrame containing processed results
        """
        try:
            logger.info(f"Processing {len(input_data)} rows in batch mode")
            aggregation_file_path = '/Users/<USER>/repos/price-crawler/application/airflow/dags/input_parameters/comparis/config/ObjectPerilStructureEFAG.xlsx'

            # Transform entire dataset to Turing format
            batch_request, indicator = self.transformer.transform_batch_data(input_data,aggregation_file_path)

            # Send single batch request to Turing API
            logger.info(f"Sending batch request with {len(batch_request.get('quoteRequests', []))} quote requests")
            response = requests.post(
                self.base_url,
                json=batch_request,
                headers=self.get_headers()
            )
            response.raise_for_status()
            response_data = response.json()

            # Process batch response (similar to legacy mapper_TuringOutputPremia)
            results_df = self.process_batch_response(response_data, input_data, indicator, args)

            logger.info(f"Batch processing completed successfully: {len(results_df)} results")
            return results_df

        except Exception as e:
            logger.error(f"Batch processing failed: {str(e)}")
            return pd.DataFrame()


    def process_batch_response(self, response_data: Dict, input_data: pd.DataFrame,
                             indicator: pd.DataFrame, args: Any) -> pd.DataFrame:
        """
        Process batch response from Turing API (similar to legacy mapper_TuringOutputPremia).

        Args:
            response_data: Raw response from Turing API
            input_data: Original input DataFrame
            indicator: Indicator DataFrame from transformation
            args: Configuration arguments

        Returns:
            DataFrame with processed results
        """
        try:
            # Placeholder for indicator usage (can be enhanced later for detailed processing)
            _ = indicator

            results = []
            quotes = response_data.get('quotes', [])

            logger.info(f"Processing {len(quotes)} quotes from batch response")

            for quote in quotes:
                try:
                    reference_id = quote.get('referenceId', 'unknown')
                    quote_data = quote.get('quote', {})

                    # Extract quote information
                    result = {
                        'CrawlerID': reference_id,
                        'status': quote_data.get('status', 'unknown'),
                        'currency': quote_data.get('currency', 'CHF'),
                        'pricing_version': quote_data.get('pricingVersion', ''),
                        'product_id': quote_data.get('product', {}).get('id', ''),
                        'product_version': quote_data.get('product', {}).get('version', ''),
                        'client_error': quote.get('clientError'),
                        'server_error': quote.get('serverError'),
                        'raw_response': json.dumps(quote)
                    }

                    # Extract premium information
                    total_premiums = quote_data.get('totalPremiums', {})
                    if total_premiums:
                        final_premiums = total_premiums.get('finalPremiums', {})
                        result.update({
                            'total_premium': final_premiums.get('forTotalPeriod'),
                            'monthly_premium': final_premiums.get('forPaymentPeriod'),
                            'premium_breakdown': json.dumps(total_premiums)
                        })

                    results.append(result)

                except Exception as e:
                    logger.error(f"Error processing quote for reference {quote.get('referenceId', 'unknown')}: {str(e)}")
                    results.append({
                        'CrawlerID': quote.get('referenceId', 'unknown'),
                        'status': 'processing_error',
                        'error': str(e),
                        'raw_response': json.dumps(quote)
                    })

            # Create results DataFrame
            if results:
                results_df = pd.DataFrame(results)

                # Add metadata
                results_df["crawler_run_date"] = pd.Timestamp.now().strftime("%Y-%m-%d")
                results_df["experiment_name"] = args.experiment_name
                results_df["project_name"] = args.project_name
                results_df["experiment_file_md5_hash"] = args.md5_hash

                # Merge with original input data (like legacy code)
                if 'CrawlerID' in input_data.columns:
                    input_data_indexed = input_data.set_index('CrawlerID')
                    results_df_indexed = results_df.set_index('CrawlerID')

                    # Merge results with input data
                    final_df = results_df_indexed.merge(
                        input_data_indexed,
                        left_index=True,
                        right_index=True,
                        how='right'
                    ).reset_index()
                else:
                    final_df = results_df

                return final_df

            return pd.DataFrame()

        except Exception as e:
            logger.error(f"Error processing batch response: {str(e)}")
            return pd.DataFrame()