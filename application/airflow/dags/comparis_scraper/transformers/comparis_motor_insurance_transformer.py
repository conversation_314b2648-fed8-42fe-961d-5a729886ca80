"""
Motor insurance data transformation utilities for Comparis.ch scraper.

This module provides transformation logic for converting between raw input data
and the structured format required by the Comparis motor insurance API. It handles:
- Date calculations and formatting
- Value mapping between internal and Comparis-specific codes
- Data validation and error handling
- Conversion between DataFrame and Pydantic model formats
"""

import logging
from typing import Dict

import pandas as pd
from comparis_scraper.constants.mappings import (
    BOOLEAN_MAP,
    DEDUCTIBLE_COLLISION_MAP,
    DEDUCTIBLE_MTPL_MAP,
    DEDUCTIBLE_PMOD_MAP,
    GARAGE_MAP,
    GENDER_MAP,
    MILEAGE_MAP,
    PURCHASE_MAP,
    RESIDENCE_MAP,
    USAGE_MAP,
)
from comparis_scraper.models.comparis_motor_insurance_request import (
    CoverageInput,
    DriverInput,
    InsuranceQuoteRequest,
    VehicleInput,
)
from comparis_scraper.transformers.base_efag_transformer import BaseEFAGTransformer, TransformationError
from comparis_scraper.utils.dataframe_utils import (
    apply_value_mappings,
    calculate_conditional_values,
    convert_columns_to_integers,
)
from comparis_scraper.utils.date_utils import format_date_to_iso
from comparis_scraper.utils.model_utils import create_model_instance
from comparis_scraper.utils.validation_utils import validate_required_columns
from pydantic import ValidationError

logger = logging.getLogger(__name__)


class ComparisMotorInsuranceTransformer(BaseEFAGTransformer):
    """
    Transformer for Comparis motor insurance data.

    This class provides methods to transform raw input data from various sources
    into the format required by the Comparis motor insurance API. It handles
    data validation, mapping, and formatting to ensure API compatibility.
    """

    @classmethod
    def map_risk_language_to_comparis(cls, input_data: pd.DataFrame) -> pd.DataFrame:
        """
        Transform risk language data to Comparis format.

        Maps internal risk language values to Comparis-specific codes and formats,
        including deductibles, vehicle details, and coverage options.

        Args:
            input_data: DataFrame containing raw input data

        Returns:
            DataFrame with transformed data in Comparis format
        """
        try:
            # Apply deductible mappings
            deductible_mappings = {
                cls.COL_DEDUCTIBLE_PMOD: DEDUCTIBLE_PMOD_MAP,
                cls.COL_DEDUCTIBLE_COLLISION: DEDUCTIBLE_COLLISION_MAP,
                "What is the Deductible for MTPL": DEDUCTIBLE_MTPL_MAP,
            }
            input_data = apply_value_mappings(input_data, deductible_mappings)

            # Convert matriculation date
            input_data[cls.COL_MATRICULATION_DATE] = pd.to_datetime(
                input_data[cls.COL_MATRICULATION_DATE], format="%d/%m/%Y"
            ).map(lambda x: x.date())

            # Convert integer columns
            integer_columns = [
                cls.COL_MAKE_ID,
                cls.COL_SERIES_ID,
                cls.COL_MODEL_ID,
                cls.COL_TYPE_ID,
                cls.COL_GEAR_TYPE_ID,
                cls.COL_VEHICLE_PRICE,
                cls.COL_EQUIPMENT_PRICE,
                cls.COL_PERSONAL_EFFECTS_SI,
                cls.COL_VEHICLE_ID,
            ]
            input_data = convert_columns_to_integers(input_data, integer_columns)

            # Apply Comparis-specific mappings
            comparis_mappings = {
                cls.COL_GENDER: GENDER_MAP,
                cls.COL_VEHICLE_USAGE: USAGE_MAP,
                cls.COL_GARAGE: GARAGE_MAP,
                cls.COL_MILEAGE: MILEAGE_MAP,
                cls.COL_PURCHASE_TYPE: PURCHASE_MAP,
                cls.COL_RESIDENCE_TYPE: RESIDENCE_MAP,
                cls.COL_MTPL: BOOLEAN_MAP,
                cls.COL_MOD: BOOLEAN_MAP,
                cls.COL_COLLISION: BOOLEAN_MAP,
                cls.COL_PARKING_DAMAGE: BOOLEAN_MAP,
                cls.COL_PERSONAL_EFFECTS: BOOLEAN_MAP,
                cls.COL_GROSS_NEGLIGENCE: BOOLEAN_MAP,
            }
            input_data = apply_value_mappings(input_data, comparis_mappings)

            # Convert residence type
            input_data[cls.COL_RESIDENCE_TYPE] = input_data[cls.COL_RESIDENCE_TYPE].astype(int)

            # Convert dates to ISO format
            date_columns = [cls.COL_BIRTH_DATE, cls.COL_LICENSE_DATE]
            for col in date_columns:
                input_data[col] = pd.to_datetime(input_data[col], format="%d/%m/%Y").map(
                    lambda x: format_date_to_iso(x)
                )

            # Calculate coverage type
            input_data[cls.COL_COVERAGE_TYPE] = calculate_conditional_values(
                [
                    (input_data[cls.COL_MOD] == False),
                    (input_data[cls.COL_MOD] == True) & (input_data[cls.COL_COLLISION] == False),
                    (input_data[cls.COL_MOD] == True) & (input_data[cls.COL_COLLISION] == True),
                ],
                [1, 2, 3],
                default=0,
            )

            return input_data

        except Exception as e:
            logger.error(f"Error mapping risk language: {str(e)}")
            raise TransformationError(f"Risk language mapping failed: {str(e)}")

    @classmethod
    def transform_and_validate(cls, input_data: pd.DataFrame) -> pd.DataFrame:
        """Main transformation method with validation."""
        try:
            # Complete list of required columns
            required_columns = [
                cls.COL_DRIVER_AGE,
                cls.COL_LICENSE_AGE,
                cls.COL_VEHICLE_AGE,
                cls.COL_GENDER,
                cls.COL_VEHICLE_USAGE,
                cls.COL_GARAGE,
                cls.COL_MILEAGE,
                cls.COL_PURCHASE_TYPE,
                cls.COL_RESIDENCE_TYPE,
                cls.COL_MOD,
                cls.COL_COLLISION,
                cls.COL_MAKE_ID,
                cls.COL_SERIES_ID,
                cls.COL_MODEL_ID,
                cls.COL_TYPE_ID,
                cls.COL_GEAR_TYPE_ID,
                cls.COL_EQUIPMENT_PRICE,
                cls.COL_CANTON,
                cls.COL_TYPE_CERTIFICATE,
                cls.COL_TOWN_ID,
                cls.COL_NATIONALITY,
                cls.COL_CRAWLER_ID,
            ]
            validate_required_columns(input_data, required_columns)

            # Calculate age variables
            transformed_data = cls.calculate_age_variables(input_data)

            # Map risk language
            transformed_data = cls.map_risk_language_to_comparis(transformed_data)

            # Validate sample row
            if not transformed_data.empty:
                sample_row = transformed_data.iloc[0]
                cls.transform_input_data(sample_row)

            logger.info("Data transformation completed successfully")
            return transformed_data

        except ValidationError as e:
            logger.error(f"Validation error in data transformation: {str(e)}")
            raise TransformationError(f"Data validation failed: {str(e)}")
        except Exception as e:
            logger.error(f"Error in data transformation: {str(e)}")
            raise TransformationError(f"Transformation failed: {str(e)}")

    @classmethod
    def transform_input_data(cls, row_data: Dict) -> Dict:
        """Transform a single row of input data to the Comparis format."""
        try:
            matriculation_date = pd.to_datetime(row_data[cls.COL_MATRICULATION_DATE])

            # Create vehicle input
            vehicle_data = {
                "matriculation_month": matriculation_date.month,
                "matriculation_year": matriculation_date.year,
                "make_id": int(row_data[cls.COL_MAKE_ID]),
                "series_id": int(row_data[cls.COL_SERIES_ID]),
                "car_model_id": int(row_data[cls.COL_MODEL_ID]),
                "gear_type_id": int(row_data[cls.COL_GEAR_TYPE_ID]),
                "type_id": int(row_data[cls.COL_TYPE_ID]),
                "equipment_price": int(row_data[cls.COL_EQUIPMENT_PRICE]),
                "is_leased": row_data[cls.COL_PURCHASE_TYPE],
                "km_per_year": row_data[cls.COL_MILEAGE],
                "usage": row_data[cls.COL_VEHICLE_USAGE],
                "garage": row_data[cls.COL_GARAGE],
                "canton": row_data[cls.COL_CANTON],
                "type_certificate": row_data[cls.COL_TYPE_CERTIFICATE],
            }
            vehicle = create_model_instance(vehicle_data, VehicleInput)

            # Create driver input
            driver_data = {
                "birth_date": row_data[cls.COL_BIRTH_DATE],
                "gender": row_data[cls.COL_GENDER],
                "town_id": str(row_data[cls.COL_TOWN_ID]),
                "nationality": row_data[cls.COL_NATIONALITY],
                "residence_permit": None if row_data[cls.COL_RESIDENCE_TYPE] == 0 else row_data[cls.COL_RESIDENCE_TYPE],
                "license_date": row_data[cls.COL_LICENSE_DATE],
            }
            driver = create_model_instance(driver_data, DriverInput)

            # Create coverage input
            coverage_data = {
                "coverage_type": row_data[cls.COL_COVERAGE_TYPE],
                "retention_partial": row_data[cls.COL_DEDUCTIBLE_PMOD],
                "retention_collision": row_data[cls.COL_DEDUCTIBLE_COLLISION],
                "has_parking_damage": row_data[cls.COL_PARKING_DAMAGE],
                "has_personal_effects": row_data[cls.COL_PERSONAL_EFFECTS],
                "has_gross_negligence": row_data[cls.COL_GROSS_NEGLIGENCE],
            }
            coverage = create_model_instance(coverage_data, CoverageInput)

            # Create and return final request
            request = InsuranceQuoteRequest(
                crawler_id=row_data[cls.COL_CRAWLER_ID], vehicle=vehicle, driver=driver, coverage=coverage
            )

            logger.debug(f"Successfully transformed input data for crawler_id: {row_data[cls.COL_CRAWLER_ID]}")
            return request.to_comparis_format()

        except KeyError as e:
            logger.error(f"Missing required field in input data: {str(e)}")
            raise TransformationError(f"Missing required field: {str(e)}")
        except ValueError as e:
            logger.error(f"Invalid value in input data: {str(e)}")
            raise TransformationError(f"Invalid value: {str(e)}")
        except Exception as e:
            logger.error(f"Error transforming input data: {str(e)}")
            raise TransformationError(f"Transformation failed: {str(e)}")
