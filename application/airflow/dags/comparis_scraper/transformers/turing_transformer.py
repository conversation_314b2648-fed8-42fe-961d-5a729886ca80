"""
Turing transformer for EFAG motor insurance data.

This module provides transformation logic for converting between raw input data
and the structured format required by the Turing API. It handles:
- Date calculations and formatting
- Value mapping between internal and Turing-specific codes
- Data validation and error handling
- Conversion between DataFrame and JSON formats
"""

import logging
from typing import Dict, Tuple, Optional, List

import pandas as pd
from pydantic import ValidationError

from comparis_scraper.constants.turing_mappings import (
    DUMMY_VEHICLE,
    DUMMY_VARIABLES_OTHER,
    mapper_basket_columns_to_priced_elements,
    mapper_basket_columns_to_risk_questions,
    DEFAULT_PRICING_VERSION,
    DEFAULT_PRODUCT_VERSION,
    DEFAULT_PRODUCT_NAME,
    DEFAULT_AGGREGATION_SHEET,
    DISPLACEMENT_COLUMN_MAPPING,
    INTEGER_COLUMNS,
    BOOLEAN_COLUMNS,
    COVERAGE_IDS,
)
from comparis_scraper.transformers.base_efag_transformer import BaseEFAGTransformer, TransformationError
from comparis_scraper.utils.dataframe_utils import convert_columns_to_integers, map_boolean_columns
from comparis_scraper.utils.validation_utils import validate_required_columns

logger = logging.getLogger(__name__)


class TuringTransformer(BaseEFAGTransformer):
    """
    Transformer for Turing motor insurance data.

    This class provides methods to transform raw input data from various sources
    into the format required by the Turing API. It handles data validation,
    mapping, and formatting to ensure API compatibility.
    """



    @classmethod
    def transform_and_validate(cls, input_data: pd.DataFrame) -> pd.DataFrame:
        """
        Main transformation method with validation.

        Args:
            input_data: DataFrame containing raw input data

        Returns:
            DataFrame with transformed and validated data
        """
        try:
            # Validate required columns
            required_columns = [
                cls.COL_DRIVER_AGE,
                cls.COL_LICENSE_AGE,
                cls.COL_VEHICLE_AGE,
                cls.COL_CRAWLER_ID,
            ]
            validate_required_columns(input_data, required_columns)

            # Calculate age variables
            transformed_data = cls.calculate_age_variables(input_data.copy())

            logger.info("Turing data transformation completed successfully")
            return transformed_data

        except ValidationError as e:
            logger.error(f"Validation error in Turing data transformation: {str(e)}")
            raise TransformationError(f"Data validation failed: {str(e)}")
        except Exception as e:
            logger.error(f"Error in Turing data transformation: {str(e)}")
            raise TransformationError(f"Transformation failed: {str(e)}")

    @classmethod
    def transform_batch_data(cls, input_data: pd.DataFrame, aggregation_file_path: str) -> Tuple[Dict, pd.DataFrame]:
        """
        Transform entire dataset to Turing format

        Args:
            input_data: DataFrame containing all input data
            aggregation_file_path: Path to aggregation Excel file

        Returns:
            Dictionary in Turing batch API format with all quote requests
        """
        try:
            processed_data = input_data.copy()
            processed_data['LEASING_DURATION'] = 0

            # Apply displacement column renaming (from legacy logic)
            processed_data = processed_data.rename(columns=DISPLACEMENT_COLUMN_MAPPING)

            # Transform entire dataset using mapper_rl2turing
            output_dict, indicator = cls.mapper_rl2turing(
                processed_data,
                DEFAULT_PRICING_VERSION,
                DEFAULT_PRODUCT_VERSION,
                DEFAULT_PRODUCT_NAME,
                aggregation_file_path,
                DEFAULT_AGGREGATION_SHEET
            )

            logger.info(f"Batch transformation successful: {len(output_dict.get('quoteRequests', []))} quote requests generated")
            return output_dict, indicator

        except Exception as e:
            logger.error(f"Error in batch transformation: {str(e)}")
            raise TransformationError(f"Batch transformation failed: {str(e)}")

    @classmethod
    def create_selection_indicator_table(cls, file_path: str, sheet_name: str) -> pd.DataFrame:
        """
        Create selection indicator table from Excel file.

        Args:
            file_path: Path to the Excel file
            sheet_name: Name of the sheet to read

        Returns:
            DataFrame with selection indicators
        """
        try:
            input_data = pd.read_excel(file_path, sheet_name=sheet_name)
            input_data.set_index(input_data.columns.tolist(), inplace=True)
            return input_data.T
        except Exception as e:
            logger.error(f"Error creating selection indicator table: {str(e)}")
            raise TransformationError(f"Failed to create indicator table: {str(e)}")

    @classmethod
    def process_row(cls, rowdata: pd.Series, indicator: pd.DataFrame, rowdata_selector_ra_base: List,
                   quote_requests_base: List, productVersion: str, pricingVersion: str, productname: str) -> None:
        """
        Process a single row for JSON conversion.

        Args:
            rowdata: Series containing row data
            indicator: DataFrame with selection indicators
            rowdata_selector_ra_base: List of risk question keys
            quote_requests_base: List to append quote requests to
            productVersion: Product version
            pricingVersion: Pricing version
            productname: Product name
        """
        try:
            # Filter risk answers based on conditions
            rowdata_selector_ra = rowdata_selector_ra_base.copy()

            # DRV_RESIDENCE_PERMIT must be dropped from answers if DRV_NATIONALITY is not in ['CH','FL']
            if 'DRV_NATIONALITY' in rowdata.index and rowdata['DRV_NATIONALITY'] not in ['CH', 'FL']:
                if 'DRV_RESIDENCE_PERMIT' in rowdata_selector_ra:
                    rowdata_selector_ra.remove('DRV_RESIDENCE_PERMIT')

            # Displacement Risk answer must not be submitted if primary question does not require it
            if 'FUEL_TYPE' in rowdata.index and rowdata['FUEL_TYPE'] == 'ELECTRIC':
                if 'DISPLACEMENT' in rowdata_selector_ra:
                    rowdata_selector_ra.remove('DISPLACEMENT')

            # Create risk answers dictionary
            risk_answers = {}
            for key in rowdata_selector_ra:
                if key in rowdata.index:
                    risk_answers[key] = rowdata[key]

            # Create priced elements with proper structure
            priced_elements = cls._create_priced_elements(rowdata)

            # Create quote request structure
            quote_request = {
                "referenceId": str(rowdata.name),
                "include": ["QUOTE_DETAILS"],
                "request": {
                    "product": {
                        "id": productname,
                        "version": productVersion
                    },
                    "pricingVersion": pricingVersion,
                    "answers": risk_answers,
                    "operationType": "FIRST",
                    "paymentFrequency": rowdata.get('PAYMENT_FREQUENCY', 'YEARLY'),
                    "volatile": True,
                    "pricedElements": priced_elements
                }
            }

            quote_requests_base.append(quote_request)

            # Update indicator table
            cls._update_indicator_table(rowdata, indicator)

        except Exception as e:
            logger.error(f"Error processing row {rowdata.name}: {str(e)}")
            # Continue processing other rows even if one fails

    @classmethod
    def _create_priced_elements(cls, rowdata: pd.Series) -> List[Dict]:
        """Create priced elements structure for Turing API."""
        priced_elements = []

        # MTPL
        mtpl_element = {
            "id": COVERAGE_IDS['MTPL'],
            "coverages": cls._make_gross_negligence(rowdata),
            "deductible": {
                "value": int(rowdata.get('Deductible_MTPL', 0) * 100)
            },
            "selected": rowdata.get('MTPL_is_selected', False)
        }
        priced_elements.append(mtpl_element)

        # MOD
        mod_element = {
            "id": COVERAGE_IDS['MOD'],
            "coverages": [
                {
                    "id": COVERAGE_IDS['COLLISION'],
                    "selected": rowdata.get('Collision_is_selected', False),
                    "deductible": {"value": int(rowdata.get('Deductible_Collision', 0) * 100)}
                },
                {
                    "id": COVERAGE_IDS['PARKED_VEHICLES'],
                    "selected": rowdata.get('ParkedVehicles_is_selected', False)
                },
                {
                    "id": COVERAGE_IDS['PURCHASE_PRICE_IND'],
                    "selected": rowdata.get('PurchasePriceInd_is_selected', False)
                }
            ] + cls._make_mod_addons(rowdata, COVERAGE_IDS['MOD']) + cls._make_gross_negligence(rowdata),
            "deductible": {
                "value": int(rowdata.get('Deductible_PMOD', 0) * 100)
            },
            "selected": rowdata.get('MOD_is_selected', False)
        }
        priced_elements.append(mod_element)

        # Glass Window
        glass_window_element = {
            "id": COVERAGE_IDS['GLASS_WINDOW'],
            "coverages": cls._make_mod_addons(rowdata, COVERAGE_IDS['GLASS_WINDOW']) + cls._make_gross_negligence(rowdata),
            "deductible": {
                "value": int(rowdata.get('Deductible_PMOD', 0) * 100)
            },
            "selected": rowdata.get('MOD_is_selected', False)
        }
        priced_elements.append(glass_window_element)

        # Glass Plus
        glass_plus_element = {
            "id": COVERAGE_IDS['GLASS_PLUS'],
            "coverages": cls._make_mod_addons(rowdata, COVERAGE_IDS['GLASS_PLUS']) + cls._make_gross_negligence(rowdata),
            "deductible": {
                "value": int(rowdata.get('Deductible_PMOD', 0) * 100)
            },
            "selected": rowdata.get('GlassExtension_is_selected', False)
        }
        priced_elements.append(glass_plus_element)

        return priced_elements

    @classmethod
    def _make_gross_negligence(cls, rowdata: pd.Series) -> List[Dict]:
        """Create gross negligence coverage structure."""
        gross_negligence = rowdata.get('GROSS_NEGLIGENCE', '').upper()

        if gross_negligence == 'YES':
            selected = True
            deductible = 0
        elif gross_negligence == 'NO':
            selected = False
            deductible = None
        else:
            selected = None
            deductible = None

        return [{
            'id': COVERAGE_IDS['GROSS_NEGLIGENCE'],
            'deductible': {
                'value': deductible
            },
            'selected': selected
        }]

    @classmethod
    def _make_mod_addons(cls, rowdata: pd.Series, prel_id: str) -> List[Dict]:
        """Create MOD addon coverages structure."""
        # Core special expenses IDs
        spec_exp_ids_core = ['BE057', 'BE001', 'BE007', 'BE051', 'BE050', 'BE012']
        locks_and_disposal = ['BE056', 'BE008']

        if prel_id == COVERAGE_IDS['MOD']:
            spec_exp_ids = spec_exp_ids_core + locks_and_disposal
        else:
            spec_exp_ids = spec_exp_ids_core

        pers_eff_id = COVERAGE_IDS['PERSONAL_EFFECTS']
        glass_plus = []

        # Personal Effects coverage
        pers_eff = [{
            'id': pers_eff_id,
            'selected': rowdata.get('PersonalEffects_is_selected', False),
            'deductible': {
                'options': None,
                'value': 0
            },
            'sumInsured': {
                'value': int(rowdata.get('PersonalEffects_sumInsured', 0) * 100)
                if rowdata.get('PersonalEffects_is_selected', False) else None
            }
        }]

        # Special Expenses coverages
        spec_exp = [{
            'id': spec_id,
            'selected': rowdata.get('SpecialExpenses_is_selected', False),
            'deductible': {
                'options': None,
                'value': 0
            },
            'sumInsured': {
                'value': int(rowdata.get('SpecialExpenses_sumInsured', 0) * 100)
                if rowdata.get('SpecialExpenses_is_selected', False) else None
            }
        } for spec_id in spec_exp_ids]

        # Glass Plus coverage (only for Glass Plus coverage ID)
        if prel_id == COVERAGE_IDS['GLASS_PLUS']:
            glass_plus = [{
                'id': COVERAGE_IDS['GLASS_PLUS_ADDON'],
                'selected': rowdata.get('GlassExtension_is_selected', False)
            }]

        return spec_exp + pers_eff + glass_plus

    @classmethod
    def _update_indicator_table(cls, rowdata: pd.Series, indicator: pd.DataFrame) -> None:
        """Update the indicator table with selection indicators."""
        try:
            # For now, implement a simplified version to avoid complex MultiIndex issues
            # The indicator table is used for downstream processing and can be enhanced later
            logger.debug(f"Updating indicator table for row {rowdata.name}")
            # TODO: Implement full indicator table logic when MultiIndex structure is clarified
            # Placeholder to avoid unused parameter warning
            _ = indicator

        except Exception as e:
            logger.error(f"Error updating indicator table for row {rowdata.name}: {str(e)}")

    @classmethod
    def _get_default_value_for_column(cls, column_name: str):
        """Get appropriate default value for a column based on its expected type."""
        if column_name in INTEGER_COLUMNS:
            return 0
        elif column_name in BOOLEAN_COLUMNS:
            return False
        elif 'DATE' in column_name.upper() or 'DOB' in column_name.upper():
            return '01/01/2000'
        else:
            return ''  # String default

    @classmethod
    def process_response(cls, response_data: Dict, request_data: Dict) -> Dict:
        """
        Process Turing API response data.

        Args:
            response_data: Raw response from Turing API
            request_data: Original request data

        Returns:
            Processed response data
        """
        try:
            # Extract key information from response
            processed_response = {
                "crawler_id": request_data.get("CrawlerID", "unknown"),
                "status": "success",
                "quotes": []
            }

            # Process quotes if available
            if "quotes" in response_data:
                for quote in response_data["quotes"]:
                    quote_info = {
                        "reference_id": quote.get("referenceId"),
                        "product_id": quote.get("quote", {}).get("product", {}).get("id"),
                        "pricing_version": quote.get("quote", {}).get("pricingVersion"),
                        "status": quote.get("quote", {}).get("status"),
                        "currency": quote.get("quote", {}).get("currency"),
                        "total_premiums": quote.get("quote", {}).get("totalPremiums", {}),
                        "client_error": quote.get("clientError"),
                        "server_error": quote.get("serverError")
                    }
                    processed_response["quotes"].append(quote_info)

            return processed_response

        except Exception as e:
            logger.error(f"Error processing Turing response: {str(e)}")
            return {
                "crawler_id": request_data.get("CrawlerID", "unknown"),
                "status": "error",
                "error": str(e),
                "quotes": []
            }

    @classmethod
    def map_string_columns(cls, output_ra: pd.DataFrame, productversion: str) -> None:
        """Map string columns to appropriate values."""
        output_ra['GROSS_NEGLIGENCE'] = output_ra['GROSS_NEGLIGENCE'].str.upper()
        output_ra['HOLDER_IS_EMPLOYEE'] = output_ra['HOLDER_IS_EMPLOYEE'].str.upper()
        output_ra['VEH_OWNER_IS_HOLDER'] = output_ra['VEH_OWNER_IS_HOLDER'].str.upper()
        output_ra['USUAL_PARK_LOCATION'] = output_ra['USUAL_PARK_LOCATION'].str.upper()
        output_ra['MAIN_DRV_IS_HOLDER'] = output_ra['MAIN_DRV_IS_HOLDER'].str.upper()

        output_ra['DRIVERS_17_25'] = output_ra['DRIVERS_17_25'].map(
            {'0': 'NO', '1': 'YES', '2': 'YES', '3P': 'YES'}) if productversion in ['CHMOTOR_COMPANIES',
                                                                                    'CHMOTOR_COMPANIES_LEASING'] else \
            output_ra['DRIVERS_17_25']
        output_ra['FUEL_TYPE'] = output_ra['DISPLACEMENT'].fillna(0).apply(
            lambda x: 'ELECTRIC' if x == 0 else 'PETROL_CAT')

    @classmethod
    def convert_table_to_json(cls, output_ra: pd.DataFrame, aggregationFilePath: str,
                              aggregation_sheet_name: str, mapper_risk_questions: Dict,
                              productVersion: str, pricingVersion: str, productname: str) -> Tuple[Dict, pd.DataFrame]:
        """Convert DataFrame to JSON format for Turing API."""
        quote_requests_base = []
        indicator = cls.create_selection_indicator_table(aggregationFilePath, aggregation_sheet_name)
        rowdata_selector_ra_base = list(mapper_risk_questions.keys())

        # Process each row
        for _, row in output_ra.iterrows():
            cls.process_row(row, indicator, rowdata_selector_ra_base, quote_requests_base,
                          productVersion, pricingVersion, productname)

        return {'quoteRequests': quote_requests_base}, indicator

    @classmethod
    def mapper_rl2turing(cls, input0: pd.DataFrame, pricingVersion: str, productVersion: str,
                         productname: str, aggregationFilePath: str, aggregation_sheet_name: str) -> Tuple[
        Dict, pd.DataFrame]:
        """
        Transform risk language data to Turing format.

        Args:
            input0: DataFrame containing raw input data
            pricingVersion: Version of the pricing engine
            productVersion: Version of the product
            productname: Name of the product
            aggregationFilePath: Path to the aggregation file
            aggregation_sheet_name: Name of the aggregation sheet

        Returns:
            Tuple containing the transformed data in Turing format and the indicator DataFrame
        """
        # Add dummy data to input, avoiding duplicates
        input_data = input0.copy()

        # Apply column renaming for displacement (from legacy logic)
        input_data = input_data.rename(columns=DISPLACEMENT_COLUMN_MAPPING)

        # Add dummy vehicle data only if columns don't already exist
        for col, value in DUMMY_VEHICLE.items():
            if col not in input_data.columns:
                input_data[col] = value

        # Add dummy other variables only if columns don't already exist
        for col, value in DUMMY_VARIABLES_OTHER.items():
            if col not in input_data.columns:
                input_data[col] = value

        # Calculate age variables
        cls.calculate_age_variables(input_data)

        # Write Output Table for Risk Answer component of request
        output_ra = pd.DataFrame()
        for y, x in mapper_basket_columns_to_risk_questions.items():
            if x in input_data.columns:
                try:
                    column_data = input_data[x]
                    logger.debug(f"Processing column '{x}' -> '{y}', type: {type(column_data)}, shape: {getattr(column_data, 'shape', 'N/A')}")

                    # Ensure we get a Series, not a DataFrame
                    if isinstance(column_data, pd.DataFrame):
                        if column_data.shape[1] == 1:
                            column_data = column_data.squeeze()  # Convert single-column DataFrame to Series
                        else:
                            logger.error(f"Column '{x}' returned DataFrame with {column_data.shape[1]} columns: {list(column_data.columns)}")
                            raise ValueError(f"Column '{x}' selection returned multiple columns")

                    output_ra.insert(0, y, column_data)
                except Exception as e:
                    logger.error(f"Error processing column '{x}' -> '{y}': {str(e)}")
                    raise
            else:
                logger.warning(f"Column '{x}' not found in input data for mapping to '{y}'")
                # Add appropriate default values based on column type
                default_value = cls._get_default_value_for_column(y)
                output_ra.insert(0, y, pd.Series([default_value] * len(input_data), index=input_data.index))

        # Add Elements for Priced Elements to Output Table
        for y, x in mapper_basket_columns_to_priced_elements.items():
            if x in input_data.columns:
                output_ra.insert(0, y, input_data[x])
            else:
                logger.warning(f"Column '{x}' not found in input data for mapping to '{y}'")
                # Add appropriate default values based on column type
                default_value = cls._get_default_value_for_column(y)
                output_ra.insert(0, y, pd.Series([default_value] * len(input_data), index=input_data.index))

        output_ra = map_boolean_columns(output_ra, [
            'MTPL_is_selected',
            'MOD_is_selected',
            'Collision_is_selected',
            'ParkedVehicles_is_selected',
            'GlassExtension_is_selected',
            'SpecialExpenses_is_selected',
            'PersonalEffects_is_selected',
            'PurchasePriceInd_is_selected'
        ])
        output_ra = convert_columns_to_integers(output_ra, ['Deductible_PMOD',
                                                            'Deductible_Collision',
                                                            'Deductible_MTPL',
                                                            'ACC_CATALOG_PRICE',
                                                            'VEH_EMPTY_WEIGHT',
                                                            'VEH_TOTAL_WEIGHT',
                                                            'DISPLACEMENT',
                                                            'VEH_POWER',
                                                            'VEH_CATALOG_PRICE',
                                                            'SpecialExpenses_sumInsured',
                                                            'PersonalEffects_sumInsured',
                                                            'LEASING_DURATION'
                                                            ])
        cls.map_string_columns(output_ra, productname)

        # Rename Input column names
        renamer_input = mapper_basket_columns_to_risk_questions.copy()
        renamer_input.update(mapper_basket_columns_to_priced_elements)
        renamer_input = {y: x for x, y in renamer_input.items()}
        input0.rename(columns=renamer_input, inplace=True)

        return cls.convert_table_to_json(
            output_ra,
            aggregationFilePath,
            aggregation_sheet_name,
            mapper_basket_columns_to_risk_questions,
            productVersion,
            pricingVersion,
            productname
        )