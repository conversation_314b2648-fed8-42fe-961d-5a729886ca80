"""
Base transformer for EFAG motor insurance data.

This module provides base transformation logic for converting between raw input data
and structured formats required by different APIs. It handles:
- Date calculations and formatting
- Common data validation and error handling
- Shared utility methods for derived transformers
"""

import logging
from datetime import date, timedelta
from typing import Dict, List

import numpy as np
import pandas as pd
from pydantic import ValidationError

from comparis_scraper.utils.date_utils import format_date_string
from comparis_scraper.utils.validation_utils import validate_required_columns

logger = logging.getLogger(__name__)


class TransformationError(Exception):
    """Custom exception for transformation errors during data processing."""
    pass


class BaseEFAGTransformer:
    """
    Base Transformer for EFAG motor insurance data.

    This class provides methods to transform raw input data from various sources
    into structured formats required by different APIs. It handles
    data validation, mapping, and formatting to ensure API compatibility.
    """

    # Core data point column names
    COL_BIRTH_DATE = "What is the main driver date of birth?"
    COL_LICENSE_DATE = "When has the main driver obtained the driving license (date)?"
    COL_MATRICULATION_DATE = "When is the 1st matriculation of the vehicle on the market?"
    COL_COVERAGE_TYPE = "CoverageType"

    # Input column names
    COL_DRIVER_AGE = "DRVAGE"
    COL_LICENSE_AGE = "AGEDRVLICENSE"
    COL_VEHICLE_AGE = "What's the time in years since the 1st matriculation date on the market?"
    COL_GENDER = "What is the main driver gender?"
    COL_VEHICLE_USAGE = "What is the intended use of the vehicle?"
    COL_GARAGE = "Garage Available?"
    COL_MILEAGE = "Annual Mileage in kilometres"
    COL_PURCHASE_TYPE = "How is the vehicle purchased?"
    COL_RESIDENCE_TYPE = "MD Residence Type"

    # Coverage columns
    COL_MOD = "MOD"
    COL_COLLISION = "Collision"
    COL_PARKING_DAMAGE = "ParkedVehicles"
    COL_PERSONAL_EFFECTS = "PersonalEffects"
    COL_GROSS_NEGLIGENCE = "GrossNegligence"
    COL_DEDUCTIBLE_PMOD = "What is the Deductible for Partial MOD?"
    COL_DEDUCTIBLE_COLLISION = "What is the Deductible for Collision"

    # Vehicle data columns
    COL_MAKE_ID = "Make_cmp_id"
    COL_SERIES_ID = "Series_cmp_id"
    COL_MODEL_ID = "Model_cmp_id"
    COL_TYPE_ID = "Type_cmp_id"
    COL_GEAR_TYPE_ID = "Geartype_cmp_id"
    COL_EQUIPMENT_PRICE = "What is the catalog price of the vehicle accessories?"
    COL_CANTON = "MD Canton"
    COL_TYPE_CERTIFICATE = "TGCODE"
    COL_VEHICLE_PRICE = "What is the catalog price of the vehicle?"

    # Driver data columns
    COL_TOWN_ID = "TownID"
    COL_NATIONALITY = "MD Nation"
    COL_CRAWLER_ID = "CrawlerID"

    COL_PERSONAL_EFFECTS_SI = "PersonalEffSI"
    COL_VEHICLE_ID = "VehicleID"
    COL_MTPL = "MTPL"
    COL_DEDUCTIBLE_MTPL = "What is the Deductible for MTPL"

    @classmethod
    def calculate_age_variables(cls, input_data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate all date-related variables from age values.

        Converts age values in years to actual dates for driver birth date,
        license date, and vehicle first matriculation date.

        Args:
            input_data: DataFrame containing raw input data with age values

        Returns:
            DataFrame with calculated date fields
        """
        try:
            reference_date = date.today()
            birthday_before_reference_days = 2
            reference_date_adjusted = reference_date - timedelta(birthday_before_reference_days)

            date_format = f"{reference_date_adjusted.day:02d}/{reference_date_adjusted.month:02d}/"

            # Calculate birthdate
            input_data[cls.COL_BIRTH_DATE] = date_format + (
                reference_date_adjusted.year - input_data[cls.COL_DRIVER_AGE].astype(int)
            ).astype(str)

            # Calculate license date
            input_data[cls.COL_LICENSE_DATE] = date_format + (
                reference_date_adjusted.year - input_data[cls.COL_LICENSE_AGE].astype(int)
            ).astype(str)

            # Calculate vehicle first matriculation
            input_data[cls.COL_MATRICULATION_DATE] = date_format + (
                reference_date_adjusted.year - input_data[cls.COL_VEHICLE_AGE].astype(int)
            ).astype(str)

            # Calculate leasing dates
            input_data["LEASING_START"] = format_date_string(reference_date)
            input_data["LEASING_DURATION"] = np.nan
            input_data["LEASING_END"] = cls._calculate_leasing_end_date(input_data["LEASING_DURATION"], reference_date)

            return input_data

        except KeyError as e:
            logger.error(f"Missing required column for age calculation: {str(e)}")
            raise TransformationError(f"Missing required column: {str(e)}")
        except Exception as e:
            logger.error(f"Error calculating age variables: {str(e)}")
            raise TransformationError(f"Age calculation failed: {str(e)}")

    @staticmethod
    def _calculate_leasing_end_date(duration_series: pd.Series, reference_date: date) -> pd.Series:
        """Helper method to calculate leasing end dates"""
        return (
            duration_series.fillna(0)
            .astype(int)
            .apply(lambda months: reference_date + pd.DateOffset(months=months))
            .dt.strftime("%d/%m/%Y")
        )