"""
Turing Motor Insurance Crawler (Simplified)

This is a simplified version that processes the entire dataset in a single batch
request, exactly like the legacy TuringCaller_EFAG_Crawler.py.

No chunk_size needed - the Turing API handles batch processing natively.
"""

import argparse
import logging
import time
import hashlib
import sys
import os
from datetime import datetime
from pathlib import Path
from typing import Optional

import pandas as pd

# Add the parent directory to Python path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from comparis_scraper.scrapers.turing_insurance_scraper import TuringInsuranceScraper
from comparis_scraper.utils.data_processing import save_results_to_csv
from pricing_analysts_adhoc_scripts.utils.file_utils import ensure_directories_exist

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger()
logger.setLevel(logging.INFO)


class TuringScraperArguments:
    """Arguments for Turing scraper processing."""
    
    def __init__(self, csv_path: Path, experiment_name: str, project_name: str):
        self.csv_path = csv_path
        self.experiment_name = experiment_name
        self.project_name = project_name
        
        # Calculate MD5 hash of the input file for tracking
        self.md5_hash = self._calculate_file_hash(csv_path)
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate MD5 hash of the input file."""
        try:
            with open(file_path, 'rb') as f:
                file_hash = hashlib.md5()
                for chunk in iter(lambda: f.read(4096), b""):
                    file_hash.update(chunk)
                return file_hash.hexdigest()
        except Exception as e:
            logger.warning(f"Could not calculate file hash: {str(e)}")
            return "unknown"


def crawl_turing_motor_insurance_simple(
    input_file_path: str,
    output_file_path: str,
    api_key: str,
    experiment_name: str = "Turing_Motor_Crawl",
    project_name: str = "Motor_Insurance_Pricing",
) -> Optional[pd.DataFrame]:
    """
    Crawl Turing motor insurance data in a single batch request (like legacy).

    Args:
        input_file_path: Path to input CSV file containing parameters
        output_file_path: Path where output CSV will be saved
        api_key: Turing API key for authentication
        experiment_name: Name of the experiment for tracking
        project_name: Name of the project for tracking

    Returns:
        DataFrame with results, or None if no data processed

    Raises:
        Exception: If an error occurs during crawling or processing
    """
    execution_start_time = time.time()

    try:
        logger.info(f"Starting Turing motor insurance crawl (single batch)")
        logger.info(f"Reading input from: {input_file_path}")

        # Validate input file exists
        input_path = Path(input_file_path)
        if not input_path.exists():
            raise FileNotFoundError(f"Input file not found: {input_file_path}")

        # Create output directory if it doesn't exist
        ensure_directories_exist(output_file_path)

        # Load input data
        logger.info("Loading input data...")
        input_data = pd.read_csv(input_path, dtype=str)
        
        # Remove empty rows
        input_data = input_data.dropna(subset=['CrawlerID'])
        logger.info(f"Loaded {len(input_data)} valid records for processing")

        if len(input_data) == 0:
            logger.warning("No valid records found in input file")
            return None

        # Initialize scraper
        scraper = TuringInsuranceScraper(api_key=api_key)
        
        # Create arguments for the scraper
        scraper_args = TuringScraperArguments(
            csv_path=input_path,
            experiment_name=experiment_name,
            project_name=project_name
        )

        logger.info(f"Processing all {len(input_data)} records in single batch request...")
        
        result_df = scraper.process_data(input_data, scraper_args)
        
        if result_df is not None and not result_df.empty:
            records_processed = len(result_df)
            
            # Log results
            if 'status' in result_df.columns:
                success_count = len(result_df[result_df['status'] == 'success'])
                success_rate = (success_count / records_processed) * 100
                logger.info(f"Processing completed: {success_count}/{records_processed} successful ({success_rate:.1f}%)")
            else:
                logger.info(f"Processing completed: {records_processed} records processed")
            
            # Save results
            logger.info("Saving results...")
            final_df = save_results_to_csv([result_df], output_file_path)
            return final_df
        else:
            logger.warning("No results found during crawling")
            return None

    except Exception as e:
        logger.error(f"Error during Turing motor insurance crawl: {str(e)}")
        raise
    finally:
        # Log execution time
        execution_time_seconds = time.time() - execution_start_time
        execution_time_minutes = execution_time_seconds / 60

        logger.info(
            f"Crawl completed in {execution_time_seconds:.2f} seconds "
            f"({execution_time_minutes:.2f} minutes)"
        )


def parse_command_line_arguments() -> argparse.Namespace:
    """
    Parse command line arguments for the crawler.

    Returns:
        Namespace containing the parsed arguments
    """
    parser = argparse.ArgumentParser(
        description="Crawl Turing API for motor insurance pricing data (single batch)",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )

    # Define timestamp for default output filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    parser.add_argument(
        "--input",
        type=str,
        help="Path to input CSV file with parameters",
        default="./../input_parameters/comparis/comparis_motor/ComparisEFAG_test.csv",
    )

    parser.add_argument(
        "--output",
        type=str,
        help="Path to output CSV file for results",
        default=f"./output_data/output_data_turing_motor_simple_{timestamp}.csv",
    )

    parser.add_argument(
        "--api-key",
        type=str,
        # required=True,
        help="Turing API key for authentication",
        default="1rZscuHRbRGplYCLli",
    )

    parser.add_argument(
        "--experiment-name",
        type=str,
        default="Turing_Motor_Crawl_Simple",
        help="Name of the experiment for tracking"
    )

    parser.add_argument(
        "--project-name", 
        type=str,
        default="Motor_Insurance_Pricing",
        help="Name of the project for tracking"
    )

    return parser.parse_args()


if __name__ == "__main__":
    # Parse command line arguments
    args = parse_command_line_arguments()

    # Run the crawler
    try:
        result_df = crawl_turing_motor_insurance_simple(
            input_file_path=args.input,
            output_file_path=args.output,
            api_key=args.api_key,
            experiment_name=args.experiment_name,
            project_name=args.project_name,
        )
        
        if result_df is not None:
            logger.info("🎉 Turing motor insurance crawl completed successfully!")
            logger.info(f"Results saved to: {args.output}")
        else:
            logger.warning("⚠️  Crawl completed but no results were generated")
            
    except KeyboardInterrupt:
        logger.info("Crawl interrupted by user")
    except Exception as e:
        logger.error(f"Crawl failed: {str(e)}")
        exit(1)
