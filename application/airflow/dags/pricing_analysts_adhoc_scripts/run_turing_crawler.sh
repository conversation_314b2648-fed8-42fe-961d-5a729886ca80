#!/bin/bash

# Turing Motor Insurance Crawler Runner
#
# This script provides an easy way to run the Turing crawler with common configurations.
# It includes validation, logging, and error handling.

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
DEFAULT_INPUT="../input_parameters/comparis/comparis_motor/ComparisEFAG_test.csv"
DEFAULT_CHUNK_SIZE=50
DEFAULT_EXPERIMENT="Turing_Motor_Crawl"
DEFAULT_PROJECT="Motor_Insurance_Pricing"

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to show usage
show_usage() {
    echo "🚀 Turing Motor Insurance Crawler"
    echo "=================================="
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Required:"
    echo "  -k, --api-key KEY        Turing API key (required)"
    echo ""
    echo "Optional:"
    echo "  -i, --input FILE         Input CSV file (default: $DEFAULT_INPUT)"
    echo "  -o, --output FILE        Output CSV file (default: auto-generated)"
    echo "  -c, --chunk-size SIZE    Batch size (default: $DEFAULT_CHUNK_SIZE)"
    echo "  -e, --experiment NAME    Experiment name (default: $DEFAULT_EXPERIMENT)"
    echo "  -p, --project NAME       Project name (default: $DEFAULT_PROJECT)"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  # Basic usage"
    echo "  $0 --api-key \"your_api_key_here\""
    echo ""
    echo "  # Custom input and output"
    echo "  $0 --api-key \"your_api_key_here\" \\"
    echo "     --input \"/path/to/input.csv\" \\"
    echo "     --output \"/path/to/output.csv\""
    echo ""
    echo "  # Production run with custom settings"
    echo "  $0 --api-key \"your_api_key_here\" \\"
    echo "     --input \"production_data.csv\" \\"
    echo "     --chunk-size 100 \\"
    echo "     --experiment \"Production_Run\" \\"
    echo "     --project \"Q4_2024_Analysis\""
}

# Parse command line arguments
API_KEY=""
INPUT_FILE="$DEFAULT_INPUT"
OUTPUT_FILE=""
CHUNK_SIZE="$DEFAULT_CHUNK_SIZE"
EXPERIMENT_NAME="$DEFAULT_EXPERIMENT"
PROJECT_NAME="$DEFAULT_PROJECT"

while [[ $# -gt 0 ]]; do
    case $1 in
        -k|--api-key)
            API_KEY="$2"
            shift 2
            ;;
        -i|--input)
            INPUT_FILE="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_FILE="$2"
            shift 2
            ;;
        -c|--chunk-size)
            CHUNK_SIZE="$2"
            shift 2
            ;;
        -e|--experiment)
            EXPERIMENT_NAME="$2"
            shift 2
            ;;
        -p|--project)
            PROJECT_NAME="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [ -z "$API_KEY" ]; then
    print_error "API key is required!"
    echo ""
    show_usage
    exit 1
fi

# Generate output filename if not provided
if [ -z "$OUTPUT_FILE" ]; then
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    OUTPUT_FILE="./output_data/turing_motor_results_${TIMESTAMP}.csv"
fi

# Validate input file exists
if [ ! -f "$INPUT_FILE" ]; then
    print_error "Input file not found: $INPUT_FILE"
    exit 1
fi

# Create output directory if it doesn't exist
OUTPUT_DIR=$(dirname "$OUTPUT_FILE")
if [ ! -d "$OUTPUT_DIR" ]; then
    mkdir -p "$OUTPUT_DIR"
    print_info "Created output directory: $OUTPUT_DIR"
fi

# Display configuration
print_info "Turing Motor Insurance Crawler Configuration"
echo "=============================================="
echo "📁 Input file:      $INPUT_FILE"
echo "📄 Output file:     $OUTPUT_FILE"
echo "🔑 API key:         ${API_KEY:0:10}..."
echo "📦 Chunk size:      $CHUNK_SIZE"
echo "🧪 Experiment:      $EXPERIMENT_NAME"
echo "📊 Project:         $PROJECT_NAME"
echo ""

# Confirm before running
read -p "Do you want to proceed? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_warning "Operation cancelled by user"
    exit 0
fi

# Run the crawler
print_info "Starting Turing crawler..."
echo ""

START_TIME=$(date +%s)

python turing_motor_insurance_crawler.py \
    --input "$INPUT_FILE" \
    --output "$OUTPUT_FILE" \
    --api-key "$API_KEY" \
    --experiment-name "$EXPERIMENT_NAME" \
    --project-name "$PROJECT_NAME" \
    --chunk-size "$CHUNK_SIZE"

EXIT_CODE=$?
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo ""
echo "=============================================="

if [ $EXIT_CODE -eq 0 ]; then
    print_success "Crawler completed successfully!"
    print_info "Execution time: ${DURATION} seconds"
    
    if [ -f "$OUTPUT_FILE" ]; then
        FILE_SIZE=$(wc -l < "$OUTPUT_FILE")
        print_info "Output file: $OUTPUT_FILE"
        print_info "Records in output: $((FILE_SIZE - 1))"  # Subtract header row
    fi
else
    print_error "Crawler failed with exit code: $EXIT_CODE"
    print_info "Check the logs above for error details"
fi

echo "=============================================="
