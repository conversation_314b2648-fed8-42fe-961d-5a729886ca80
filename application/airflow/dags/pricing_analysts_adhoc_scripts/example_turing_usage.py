#!/usr/bin/env python3
"""
Example usage of the Turing Motor Insurance Crawler.

This script demonstrates how to use the crawler programmatically
and provides examples for different use cases.
"""

import logging
from datetime import datetime
from pathlib import Path

from turing_motor_insurance_crawler import crawl_turing_motor_insurance

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def example_basic_usage():
    """Example: Basic crawler usage with test data."""
    logger.info("📋 Example 1: Basic Usage")
    
    # Configuration
    input_file = "../input_parameters/comparis/comparis_motor/ComparisEFAG_test.csv"
    output_file = f"./output_data/basic_example_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    api_key = "your_turing_api_key_here"  # Replace with actual API key
    
    try:
        result_df = crawl_turing_motor_insurance(
            input_file_path=input_file,
            output_file_path=output_file,
            api_key=api_key,
            chunk_size=10  # Small chunks for testing
        )
        
        if result_df is not None:
            logger.info(f"✅ Basic example completed successfully!")
            logger.info(f"📊 Processed {len(result_df)} records")
            logger.info(f"📁 Results saved to: {output_file}")
        else:
            logger.warning("⚠️  No results generated")
            
    except Exception as e:
        logger.error(f"❌ Basic example failed: {str(e)}")


def example_production_usage():
    """Example: Production usage with larger dataset and custom settings."""
    logger.info("📋 Example 2: Production Usage")
    
    # Configuration for production
    input_file = "../input_parameters/comparis/comparis_motor/ComparisEFAG_production.csv"
    output_file = f"./output_data/production_run_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    api_key = "your_production_turing_api_key_here"  # Replace with actual API key
    
    try:
        result_df = crawl_turing_motor_insurance(
            input_file_path=input_file,
            output_file_path=output_file,
            api_key=api_key,
            experiment_name="Production_Pricing_Analysis",
            project_name="Q4_2024_Motor_Insurance",
            chunk_size=100  # Larger chunks for efficiency
        )
        
        if result_df is not None:
            logger.info(f"✅ Production example completed successfully!")
            logger.info(f"📊 Processed {len(result_df)} records")
            
            # Show success rate
            if 'status' in result_df.columns:
                success_count = len(result_df[result_df['status'] == 'success'])
                total_count = len(result_df)
                success_rate = (success_count / total_count) * 100
                logger.info(f"📈 Success rate: {success_rate:.1f}% ({success_count}/{total_count})")
            
            logger.info(f"📁 Results saved to: {output_file}")
        else:
            logger.warning("⚠️  No results generated")
            
    except FileNotFoundError:
        logger.warning("⚠️  Production file not found - this is expected in test environment")
    except Exception as e:
        logger.error(f"❌ Production example failed: {str(e)}")


def example_custom_experiment():
    """Example: Custom experiment with specific tracking."""
    logger.info("📋 Example 3: Custom Experiment")
    
    # Configuration for custom experiment
    input_file = "../input_parameters/comparis/comparis_motor/ComparisEFAG_test.csv"
    output_file = f"./output_data/custom_experiment_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    api_key = "your_turing_api_key_here"  # Replace with actual API key
    
    try:
        result_df = crawl_turing_motor_insurance(
            input_file_path=input_file,
            output_file_path=output_file,
            api_key=api_key,
            experiment_name="Price_Sensitivity_Analysis",
            project_name="Motor_Insurance_Research",
            chunk_size=25  # Medium chunk size
        )
        
        if result_df is not None:
            logger.info(f"✅ Custom experiment completed successfully!")
            logger.info(f"📊 Processed {len(result_df)} records")
            
            # Show detailed results
            if 'total_premium' in result_df.columns:
                premiums = result_df['total_premium'].dropna()
                if len(premiums) > 0:
                    logger.info(f"💰 Premium range: {premiums.min():.2f} - {premiums.max():.2f} CHF")
                    logger.info(f"💰 Average premium: {premiums.mean():.2f} CHF")
            
            logger.info(f"📁 Results saved to: {output_file}")
        else:
            logger.warning("⚠️  No results generated")
            
    except Exception as e:
        logger.error(f"❌ Custom experiment failed: {str(e)}")


def example_error_handling():
    """Example: Demonstrating error handling capabilities."""
    logger.info("📋 Example 4: Error Handling")
    
    # Configuration with intentional issues for demonstration
    input_file = "nonexistent_file.csv"  # This will cause an error
    output_file = f"./output_data/error_example_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    api_key = "invalid_api_key"  # This will cause API errors
    
    try:
        result_df = crawl_turing_motor_insurance(
            input_file_path=input_file,
            output_file_path=output_file,
            api_key=api_key,
            chunk_size=5
        )
        
        # This shouldn't be reached due to file not found error
        logger.info(f"✅ Error example completed: {len(result_df) if result_df else 0} records")
        
    except FileNotFoundError as e:
        logger.info(f"✅ Expected error caught: {str(e)}")
        logger.info("This demonstrates proper error handling for missing files")
    except Exception as e:
        logger.info(f"✅ Error handled gracefully: {str(e)}")
        logger.info("This demonstrates robust error handling")


def example_batch_size_comparison():
    """Example: Comparing different batch sizes for performance."""
    logger.info("📋 Example 5: Batch Size Comparison")
    
    input_file = "../input_parameters/comparis/comparis_motor/ComparisEFAG_test.csv"
    api_key = "your_turing_api_key_here"  # Replace with actual API key
    
    # Test different chunk sizes
    chunk_sizes = [5, 10, 20]
    
    for chunk_size in chunk_sizes:
        logger.info(f"🧪 Testing chunk size: {chunk_size}")
        
        output_file = f"./output_data/batch_test_{chunk_size}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        try:
            import time
            start_time = time.time()
            
            result_df = crawl_turing_motor_insurance(
                input_file_path=input_file,
                output_file_path=output_file,
                api_key=api_key,
                experiment_name=f"Batch_Size_Test_{chunk_size}",
                project_name="Performance_Analysis",
                chunk_size=chunk_size
            )
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            if result_df is not None:
                records_per_second = len(result_df) / execution_time if execution_time > 0 else 0
                logger.info(f"📊 Chunk size {chunk_size}: {len(result_df)} records in {execution_time:.2f}s ({records_per_second:.2f} rec/s)")
            else:
                logger.info(f"📊 Chunk size {chunk_size}: No results")
                
        except Exception as e:
            logger.warning(f"⚠️  Chunk size {chunk_size} failed: {str(e)}")


def main():
    """Run all examples."""
    logger.info("🚀 Starting Turing Crawler Examples...")
    logger.info("=" * 60)
    
    # Note: These examples will fail with real API calls unless you have a valid API key
    # They demonstrate the usage patterns and error handling
    
    examples = [
        ("Basic Usage", example_basic_usage),
        ("Production Usage", example_production_usage),
        ("Custom Experiment", example_custom_experiment),
        ("Error Handling", example_error_handling),
        ("Batch Size Comparison", example_batch_size_comparison),
    ]
    
    for name, example_func in examples:
        logger.info(f"\n{'='*60}")
        logger.info(f"Running: {name}")
        logger.info(f"{'='*60}")
        
        try:
            example_func()
        except Exception as e:
            logger.error(f"Example '{name}' failed: {str(e)}")
        
        logger.info(f"Completed: {name}")
    
    logger.info(f"\n{'='*60}")
    logger.info("🎉 All examples completed!")
    logger.info("💡 To run with real data, replace 'your_turing_api_key_here' with your actual API key")
    logger.info("📚 See README_turing_crawler.md for detailed documentation")


if __name__ == "__main__":
    main()
