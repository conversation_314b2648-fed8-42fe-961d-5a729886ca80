# Turing Motor Insurance Crawler

A comprehensive crawler for fetching motor insurance pricing data from the Turing API using batch processing.

## Overview

This crawler is designed to replace the legacy `TuringCaller_EFAG_Crawler.py` with a modern, maintainable solution that:

- ✅ **Batch Processing**: Processes multiple quotes in single API calls (like legacy)
- ✅ **Efficient**: Reduces API calls and network overhead
- ✅ **Robust**: Comprehensive error handling and retry logic
- ✅ **Configurable**: Flexible chunk sizes and experiment tracking
- ✅ **Compatible**: Uses the same input format as legacy crawler

## Features

### 🚀 **Performance**
- **Batch API calls**: Multiple quote requests in single HTTP request
- **Chunked processing**: Configurable batch sizes for memory efficiency
- **Progress tracking**: Real-time logging of processing status

### 🛡️ **Reliability**
- **Error handling**: Graceful handling of API failures
- **Chunk isolation**: Failed chunks don't stop entire process
- **Input validation**: Comprehensive validation of input data

### 📊 **Monitoring**
- **Success rate tracking**: Detailed statistics on processing results
- **Execution timing**: Performance metrics and timing information
- **Experiment tracking**: MD5 hashing and metadata for reproducibility

## Usage

### Basic Usage

```bash
python turing_motor_insurance_crawler.py \
    --input "../input_parameters/comparis/comparis_motor/ComparisEFAG_test.csv" \
    --output "./output_data/turing_results_20241213.csv" \
    --api-key "your_turing_api_key_here"
```

### Advanced Usage

```bash
python turing_motor_insurance_crawler.py \
    --input "../input_parameters/comparis/comparis_motor/ComparisEFAG_production.csv" \
    --output "./output_data/turing_production_20241213.csv" \
    --api-key "your_turing_api_key_here" \
    --experiment-name "Production_Pricing_Analysis" \
    --project-name "Q4_2024_Motor_Insurance" \
    --chunk-size 100
```

### Command Line Arguments

| Argument | Description | Default | Required |
|----------|-------------|---------|----------|
| `--input` | Path to input CSV file | `ComparisEFAG_test.csv` | No |
| `--output` | Path to output CSV file | `output_data_turing_motor_{timestamp}.csv` | No |
| `--api-key` | Turing API key | None | **Yes** |
| `--experiment-name` | Experiment name for tracking | `Turing_Motor_Crawl` | No |
| `--project-name` | Project name for tracking | `Motor_Insurance_Pricing` | No |
| `--chunk-size` | Records per batch | `50` | No |

## Input Data Format

The crawler expects a CSV file with the same format as the legacy `ComparisEFAG.csv`:

### Required Columns
- `CrawlerID` - Unique identifier for each quote request
- `DRVAGE` - Driver age
- `AGEDRVLICENSE` - Age when driver got license
- `What is the vehicles displacement (Cubic capacity)?` - Engine displacement
- `What's the time in years since the 1st matriculation date on the market?` - Vehicle age

### Example Input
```csv
CrawlerID,DRVAGE,AGEDRVLICENSE,"What is the vehicles displacement (Cubic capacity)?","What's the time in years since the 1st matriculation date on the market?"
0,19,18,1490,3
1,45,18,2993,8
```

## Output Data Format

The crawler generates a comprehensive CSV file with:

### Core Results
- `CrawlerID` - Original identifier
- `status` - Processing status (success/failed)
- `total_premium` - Total insurance premium
- `currency` - Premium currency (CHF)

### API Response Data
- `product_id` - Turing product ID (CHMOTOR)
- `product_version` - Product version
- `pricing_version` - Pricing model version
- `raw_response` - Complete API response (JSON)

### Metadata
- `crawler_run_date` - Execution date
- `experiment_name` - Experiment identifier
- `project_name` - Project identifier
- `experiment_file_md5_hash` - Input file hash

### Original Input Data
All original columns from the input CSV are preserved and merged with results.

## Architecture

### Components

1. **TuringInsuranceScraper**: Handles API communication and batch processing
2. **TuringTransformer**: Transforms input data to Turing API format
3. **Data Processing Utils**: CSV handling and result aggregation
4. **File Utils**: Directory management and file operations

### Processing Flow

```
Input CSV → Data Validation → Batch Transformation → API Request → Response Processing → Output CSV
```

### Batch Processing

The crawler processes data in configurable chunks:

1. **Load Input**: Read and validate CSV data
2. **Create Chunks**: Split data into manageable batches
3. **Transform**: Convert each chunk to Turing API format
4. **API Call**: Send batch request to Turing API
5. **Process Response**: Parse and merge results
6. **Save Results**: Combine chunks and save to CSV

## Error Handling

### Chunk-Level Errors
- Failed chunks are logged but don't stop processing
- Successful chunks are still saved
- Detailed error messages for debugging

### API Errors
- Invalid API keys are detected early
- Network timeouts are handled gracefully
- Malformed responses are logged with details

### Data Errors
- Missing required columns are validated upfront
- Empty or invalid rows are filtered out
- Data type mismatches are handled

## Performance Considerations

### Optimal Chunk Sizes
- **Small datasets (< 100 rows)**: chunk_size = 10-20
- **Medium datasets (100-1000 rows)**: chunk_size = 50-100
- **Large datasets (> 1000 rows)**: chunk_size = 100-200

### API Rate Limits
The Turing API has rate limits. The crawler:
- Uses batch processing to minimize API calls
- Includes built-in delays between chunks
- Provides configurable chunk sizes for rate limit management

## Monitoring and Logging

### Log Levels
- **INFO**: Progress updates and statistics
- **WARNING**: Non-fatal issues and fallbacks
- **ERROR**: Failed chunks and API errors

### Key Metrics
- Total processing time
- Records processed per second
- Success rate percentage
- API response times

## Troubleshooting

### Common Issues

1. **"No module named 'comparis_scraper'"**
   - Ensure you're running from the correct directory
   - Check Python path includes the dags directory

2. **"Invalid API key"**
   - Verify your Turing API key is correct
   - Check API key has proper permissions

3. **"No valid records found"**
   - Verify input CSV has required columns
   - Check for empty CrawlerID values

4. **"Batch processing failed"**
   - Check API rate limits
   - Verify network connectivity
   - Review API response format

### Debug Mode

For detailed debugging, modify the logging level:

```python
logging.basicConfig(level=logging.DEBUG)
```

## Migration from Legacy

### Differences from TuringCaller_EFAG_Crawler.py

| Aspect | Legacy | New Crawler |
|--------|--------|-------------|
| **Architecture** | Monolithic script | Modular OOP design |
| **Error Handling** | Basic | Comprehensive |
| **Logging** | Minimal | Detailed with metrics |
| **Configuration** | Hardcoded | Command-line arguments |
| **Testing** | None | Integration tests |
| **Maintainability** | Low | High |

### Migration Steps

1. **Backup**: Save existing legacy results
2. **Test**: Run new crawler on small dataset
3. **Compare**: Verify output format matches expectations
4. **Deploy**: Replace legacy crawler in production
5. **Monitor**: Track performance and success rates

## Examples

### Production Run
```bash
# Process full production dataset
python turing_motor_insurance_crawler.py \
    --input "/data/comparis/ComparisEFAG_production.csv" \
    --output "/results/turing_production_$(date +%Y%m%d).csv" \
    --api-key "$TURING_API_KEY" \
    --experiment-name "Production_Daily_Run" \
    --chunk-size 100
```

### Development Testing
```bash
# Test with small dataset
python turing_motor_insurance_crawler.py \
    --input "../input_parameters/comparis/comparis_motor/ComparisEFAG_test.csv" \
    --output "./test_results.csv" \
    --api-key "$TURING_TEST_API_KEY" \
    --chunk-size 10
```

The Turing Motor Insurance Crawler provides a modern, efficient, and maintainable solution for fetching motor insurance pricing data while maintaining full compatibility with existing data formats and workflows.
