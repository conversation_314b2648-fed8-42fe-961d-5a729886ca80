crawler_id,status,error,results,input_request,retry_attempts,crawler_run_date,experiment_filename,experiment_name,experiment_file_md5_hash
0,success,,"{""ResultList"": [{""ResultItemDetail"": {""LiabilityCoverPremium"": 556.4, ""LiabilityCoverDeductibleYoungDriver"": 1000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Smile_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1072.0, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Smile_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 89.8}, ""ProductId"": 10, ""ProviderId"": 5, ""ProviderNameInfobase"": ""AV8_DB_Provider_smiledirect"", ""ProductNameInfobase"": ""AV8_Productname_Smile_Clever_Tabtop"", ""LogoName"": ""Smile_alkmtg"", ""ComparisSatisfactionGrade"": 5.2, ""ComparisAward"": 2, ""IsWinner"": false, ""OfferQuote"": 1718.2, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_Smile""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""smile"", ""ProductName"": ""clever"", ""ProductType"": ""Standard"", ""ProductPosition"": 1, ""ProductId"": ""10"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 5, ""TitleInfobaseKey"": ""AV_ResultPage_DetailSheet_30D"", ""DescriptionInfobaseKey"": ""AV_ResultPage_DetailSheet_30DInfoText"", ""IconName"": ""faFileTimes"", ""Visibility"": 2}, {""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 556.4, ""LiabilityCoverDeductibleYoungDriver"": 1000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Smile_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1141.2, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Smile_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 93.3}, ""ProductId"": 31, ""ProviderId"": 5, ""ProviderNameInfobase"": ""AV8_DB_Provider_smiledirect"", ""ProductNameInfobase"": ""AV8_Productname_Smile_premium_Tabtop"", ""LogoName"": ""Smile_alkmtg"", ""ComparisSatisfactionGrade"": 5.2, ""ComparisAward"": 2, ""IsWinner"": false, ""OfferQuote"": 1790.9, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_Smile""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""smile"", ""ProductName"": ""premium"", ""ProductType"": ""Standard"", ""ProductPosition"": 2, ""ProductId"": ""31"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 5, ""TitleInfobaseKey"": ""AV_ResultPage_DetailSheet_30D"", ""DescriptionInfobaseKey"": ""AV_ResultPage_DetailSheet_30DInfoText"", ""IconName"": ""faFileTimes"", ""Visibility"": 2}, {""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 617.65, ""LiabilityCoverDeductibleYoungDriver"": null, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 1000.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1196.83, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 100.05}, ""ProductId"": 1085, ""ProviderId"": 1, ""ProviderNameInfobase"": ""AV8_DB_Provider_AxAWinterthur"", ""ProductNameInfobase"": ""AV_Productname_AXA_FLEX"", ""LogoName"": ""AXA_n5xnpq"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 1914.53, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AXAPartnerGarage""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""axa"", ""ProductName"": ""flex"", ""ProductType"": ""Standard"", ""ProductPosition"": 3, ""ProductId"": ""1085"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 697.0, ""LiabilityCoverDeductibleYoungDriver"": 1500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_ELVIA_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1199.0, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_ELVIA_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 104.3}, ""ProductId"": 29, ""ProviderId"": 25, ""ProviderNameInfobase"": ""AV8_DB_Provider_Elvia"", ""ProductNameInfobase"": ""AV8_Productname_Allianz24_Tabtop"", ""LogoName"": ""Elvia_a2ay99"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2000.3, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AZElvia""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""elvia by allianz"", ""ProductName"": ""nice"", ""ProductType"": ""Standard"", ""ProductPosition"": 4, ""ProductId"": ""29"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 653.4, ""LiabilityCoverDeductibleYoungDriver"": 2000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_ELVIA_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 1000.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1243.1, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_ELVIA_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 104.0}, ""ProductId"": 56, ""ProviderId"": 25, ""ProviderNameInfobase"": ""AV8_DB_Provider_Elvia"", ""ProductNameInfobase"": ""AV8_Productname_Allianz24_Small_Tabtop"", ""LogoName"": ""Elvia_a2ay99"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2000.5, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AZElvia""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""elvia by allianz"", ""ProductName"": ""fair"", ""ProductType"": ""Standard"", ""ProductPosition"": 5, ""ProductId"": ""56"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 726.55, ""LiabilityCoverDeductibleYoungDriver"": null, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 1000.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1408.28, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 116.99}, ""ProductId"": 1086, ""ProviderId"": 1, ""ProviderNameInfobase"": ""AV8_DB_Provider_AxAWinterthur"", ""ProductNameInfobase"": ""AV_Productname_AXA_FLEXPlus"", ""LogoName"": ""AXA_n5xnpq"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2251.82, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AXAPartnerGarage""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""axa"", ""ProductName"": ""flex plus"", ""ProductType"": ""Standard"", ""ProductPosition"": 6, ""ProductId"": ""1086"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 769.9, ""LiabilityCoverDeductibleYoungDriver"": 2000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Allianz_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 1000.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1372.3, ""ComprehensiveCoverDeductibleYoungDriver"": 2000, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Allianz_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 117.1}, ""ProductId"": 57, ""ProviderId"": 8, ""ProviderNameInfobase"": ""AV8_DB_Provider_AllianzSuisse"", ""ProductNameInfobase"": ""AV8_Productname_Allianz_Budget_Tabtop"", ""LogoName"": ""Allianz_cgzi2h"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2259.3, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AZElvia""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""allianz suisse"", ""ProductName"": ""smart"", ""ProductType"": ""Standard"", ""ProductPosition"": 7, ""ProductId"": ""57"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 809.7, ""LiabilityCoverDeductibleYoungDriver"": 0.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 0.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 556.3, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 609.0, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 121.9}, ""ProductId"": 68, ""ProviderId"": 9, ""ProviderNameInfobase"": ""AV8_DB_Provider_Generali"", ""ProductNameInfobase"": ""AV8_Productname_Generali_Optima_Tabtop"", ""LogoName"": ""Generali_qawnha"", ""ComparisSatisfactionGrade"": 5.0, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2263.9, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": null, ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""generali"", ""ProductName"": ""optima"", ""ProductType"": ""Standard"", ""ProductPosition"": 8, ""ProductId"": ""68"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 8, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 1}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 851.2, ""LiabilityCoverDeductibleYoungDriver"": 1500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Allianz_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1372.3, ""ComprehensiveCoverDeductibleYoungDriver"": 2000, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Allianz_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 121.8}, ""ProductId"": 73, ""ProviderId"": 8, ""ProviderNameInfobase"": ""AV8_DB_Provider_AllianzSuisse"", ""ProductNameInfobase"": ""AV8_Productname_Allianz_Autoversicherung"", ""LogoName"": ""Allianz_cgzi2h"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2345.3, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AZElvia""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""allianz suisse"", ""ProductName"": ""premium"", ""ProductType"": ""Standard"", ""ProductPosition"": 9, ""ProductId"": ""73"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 1058.8, ""LiabilityCoverDeductibleYoungDriver"": 1500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 226.8, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 948.4, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 123.3}, ""ProductId"": 1076, ""ProviderId"": 6, ""ProviderNameInfobase"": ""AV8_DB_Provider_Zurich"", ""ProductNameInfobase"": ""AV8_Productname_ZurichOptima_Tabtop"", ""LogoName"": ""Zurich_lvhgcv"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2373.1, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV8_DB_footnote_ZurichConnect_Tabtop""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""zurich"", ""ProductName"": ""optimum"", ""ProductType"": ""Standard"", ""ProductPosition"": 10, ""ProductId"": ""1076"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 1095.4, ""LiabilityCoverDeductibleYoungDriver"": 1000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 0.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 226.8, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 948.4, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 125.5}, ""ProductId"": 59, ""ProviderId"": 6, ""ProviderNameInfobase"": ""AV8_DB_Provider_Zurich"", ""ProductNameInfobase"": ""AV8_Productname_ZurichFlex_Tabtop"", ""LogoName"": ""Zurich_lvhgcv"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2411.9, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV8_DB_footnote_ZurichConnect_Tabtop""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""zurich"", ""ProductName"": ""premium"", ""ProductType"": ""Standard"", ""ProductPosition"": 11, ""ProductId"": ""59"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 2290.2, ""LiabilityCoverDeductibleYoungDriver"": 1500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Basler_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": null, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Basler_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 123.2}, ""ProductId"": 61, ""ProviderId"": 23, ""ProviderNameInfobase"": ""AV8_DB_Provider_BaloiseDirect"", ""ProductNameInfobase"": ""AV8_Productname_baloisedirect_medium_Tabtop"", ""LogoName"": ""Basler_rwxd0n"", ""ComparisSatisfactionGrade"": 5.2, ""ComparisAward"": 2, ""IsWinner"": false, ""OfferQuote"": 2413.4, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV8_DB_footnote_baloisedirect.ch_Tabtop""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""baloise"", ""ProductName"": ""baloisedirect m"", ""ProductType"": ""Standard"", ""ProductPosition"": 12, ""ProductId"": ""61"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 809.7, ""LiabilityCoverDeductibleYoungDriver"": 0.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 0.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 646.8, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 708.1, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 127.7}, ""ProductId"": 67, ""ProviderId"": 9, ""ProviderNameInfobase"": ""AV8_DB_Provider_Generali"", ""ProductNameInfobase"": ""AV8_Productname_Generali_Classic_Tabtop"", ""LogoName"": ""Generali_qawnha"", ""ComparisSatisfactionGrade"": 5.0, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2474.2, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": null, ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""generali"", ""ProductName"": ""classic"", ""ProductType"": ""Standard"", ""ProductPosition"": 13, ""ProductId"": ""67"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 8, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 1}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 2409.9, ""LiabilityCoverDeductibleYoungDriver"": 1500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": null, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 129.5}, ""ProductId"": 75, ""ProviderId"": 2, ""ProviderNameInfobase"": ""AV8_DB_Provider_Tcs"", ""ProductNameInfobase"": ""AV8_Productname_TCS_M_Tabtop"", ""LogoName"": ""TCS_logo_dklppf"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2539.4, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_TCS_Partnergarage""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""touring club schweiz (tcs)"", ""ProductName"": ""m \u2013 die smarte wahl"", ""ProductType"": ""Standard"", ""ProductPosition"": 14, ""ProductId"": ""75"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 3522.7, ""LiabilityCoverDeductibleYoungDriver"": 1000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Basler_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 0.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 0, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": false, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": null, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Basler_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 500, ""IsRequestedComprehensiveCoverDeductible"": false, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 182.7}, ""ProductId"": 62, ""ProviderId"": 23, ""ProviderNameInfobase"": ""AV8_DB_Provider_BaloiseDirect"", ""ProductNameInfobase"": ""AV8_Productname_baloisedirect_large_Tabtop"", ""LogoName"": ""Basler_rwxd0n"", ""ComparisSatisfactionGrade"": 5.2, ""ComparisAward"": 2, ""IsWinner"": false, ""OfferQuote"": 3705.4, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV8_DB_footnote_baloisedirect.ch_Tabtop""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""baloise"", ""ProductName"": ""baloisedirect l"", ""ProductType"": ""Standard"", ""ProductPosition"": 15, ""ProductId"": ""62"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 3667.7, ""LiabilityCoverDeductibleYoungDriver"": 1000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 0.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 0, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": false, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": null, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 500, ""IsRequestedComprehensiveCoverDeductible"": false, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 190.1}, ""ProductId"": 76, ""ProviderId"": 2, ""ProviderNameInfobase"": ""AV8_DB_Provider_Tcs"", ""ProductNameInfobase"": ""AV8_Productname_TCS_L_Tabtop"", ""LogoName"": ""TCS_logo_dklppf"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 3857.8, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_TCS_Partnergarage""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""touring club schweiz (tcs)"", ""ProductName"": ""l \u2013 von allem das beste"", ""ProductType"": ""Standard"", ""ProductPosition"": 16, ""ProductId"": ""76"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 617.65, ""LiabilityCoverDeductibleYoungDriver"": null, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 1000.0, ""HasLiabilityBonusCover"": false, ""HasComprehensiveBonusCover"": false, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": false, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 770.71, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": false, ""HasParkingDamage"": false, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 78.35}, ""ProductId"": 1087, ""ProviderId"": 1, ""ProviderNameInfobase"": ""AV8_DB_Provider_AxAWinterthur"", ""ProductNameInfobase"": ""AV_Productname_AXA_FLEXMinima"", ""LogoName"": ""AXA_n5xnpq"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 1466.71, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AXAPartnerGarage""], ""BonusProtectionCoverageMark"": 2, ""ParkingDamagesCoverageMark"": 2, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 3, ""PersonalEffectsCoverageMark"": 2, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""axa"", ""ProductName"": ""flex minima"", ""ProductType"": ""Standard"", ""ProductPosition"": 1, ""ProductId"": ""1087"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Non Matching Result List""}, ""ResultCategory"": 6, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 556.4, ""LiabilityCoverDeductibleYoungDriver"": 1000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Smile_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 979.2, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Smile_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": true, ""HasGrossNegligence"": true, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 85.2}, ""ProductId"": 30, ""ProviderId"": 5, ""ProviderNameInfobase"": ""AV8_DB_Provider_smiledirect"", ""ProductNameInfobase"": ""AV8_Productname_Smile_budget_Tabtop"", ""LogoName"": ""Smile_alkmtg"", ""ComparisSatisfactionGrade"": 5.2, ""ComparisAward"": 2, ""IsWinner"": false, ""OfferQuote"": 1620.8, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_Smile""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 3, ""GrossNegligenceCoverageMark"": 0, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""smile"", ""ProductName"": ""budget"", ""ProductType"": ""Standard"", ""ProductPosition"": 2, ""ProductId"": ""30"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Non Matching Result List""}, ""ResultCategory"": 6, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 5, ""TitleInfobaseKey"": ""AV_ResultPage_DetailSheet_30D"", ""DescriptionInfobaseKey"": ""AV_ResultPage_DetailSheet_30DInfoText"", ""IconName"": ""faFileTimes"", ""Visibility"": 2}, {""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 686.9, ""LiabilityCoverDeductibleYoungDriver"": 500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 179.0, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 580.0, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": false, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 90.1}, ""ProductId"": 66, ""ProviderId"": 9, ""ProviderNameInfobase"": ""AV8_DB_Provider_Generali"", ""ProductNameInfobase"": ""AV8_Productname_Generali_Minima_Tabtop"", ""LogoName"": ""Generali_qawnha"", ""ComparisSatisfactionGrade"": 5.0, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 1691.7, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": null, ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 2, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""generali"", ""ProductName"": ""minima"", ""ProductType"": ""Standard"", ""ProductPosition"": 3, ""ProductId"": ""66"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Non Matching Result List""}, ""ResultCategory"": 6, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 8, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 1}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 1719.7, ""LiabilityCoverDeductibleYoungDriver"": 1500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Basler_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": false, ""HasComprehensiveBonusCover"": false, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": null, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Basler_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": false, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": false, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 94.0}, ""ProductId"": 60, ""ProviderId"": 23, ""ProviderNameInfobase"": ""AV8_DB_Provider_BaloiseDirect"", ""ProductNameInfobase"": ""AV8_Productname_baloisedirect_small_Tabtop"", ""LogoName"": ""Basler_rwxd0n"", ""ComparisSatisfactionGrade"": 5.2, ""ComparisAward"": 2, ""IsWinner"": false, ""OfferQuote"": 1813.7, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV8_DB_footnote_baloisedirect.ch_Tabtop""], ""BonusProtectionCoverageMark"": 2, ""ParkingDamagesCoverageMark"": 2, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""baloise"", ""ProductName"": ""baloisedirect s"", ""ProductType"": ""Standard"", ""ProductPosition"": 4, ""ProductId"": ""60"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Non Matching Result List""}, ""ResultCategory"": 6, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 1812.0, ""LiabilityCoverDeductibleYoungDriver"": 1500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": false, ""HasComprehensiveBonusCover"": false, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": null, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": false, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": false, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 98.8}, ""ProductId"": 74, ""ProviderId"": 2, ""ProviderNameInfobase"": ""AV8_DB_Provider_Tcs"", ""ProductNameInfobase"": ""AV8_Productname_TCS_Tabtop"", ""LogoName"": ""TCS_logo_dklppf"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 1910.8, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_TCS_Partnergarage""], ""BonusProtectionCoverageMark"": 2, ""ParkingDamagesCoverageMark"": 2, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""touring club schweiz (tcs)"", ""ProductName"": ""s \u2013 sparen beim fahren"", ""ProductType"": ""Standard"", ""ProductPosition"": 5, ""ProductId"": ""74"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Non Matching Result List""}, ""ResultCategory"": 6, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 808.4, ""LiabilityCoverDeductibleYoungDriver"": 2000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 1000.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 226.8, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 500.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 878.1, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 2000.0, ""HasPersonalEffects"": false, ""HasParkingDamage"": false, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 106.0}, ""ProductId"": 58, ""ProviderId"": 6, ""ProviderNameInfobase"": ""AV8_DB_Provider_Zurich"", ""ProductNameInfobase"": ""AV8_Productname_ZurichBasic_Tabtop"", ""LogoName"": ""Zurich_lvhgcv"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2019.3, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV8_DB_footnote_ZurichConnect_Tabtop""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 2, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 2, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""zurich"", ""ProductName"": ""basic"", ""ProductType"": ""Standard"", ""ProductPosition"": 6, ""ProductId"": ""58"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Non Matching Result List""}, ""ResultCategory"": 6, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}], ""IsFinished"": true}","{""Header"": {""Language"": ""en""}, ""UserEmail"": """", ""OptimatisToggleState"": true, ""InsuranceStartDate"": ""2025-09-09T15:59:23.000Z"", ""CarInput"": {""ImmatriculationMonth"": 6, ""ImmatriculationYear"": 2025, ""SearchMode"": 3, ""CarMake"": 171272, ""CarModelSeries"": 172336, ""CarModel"": 205134, ""CarGearType"": 180007, ""CarType"": 102228861, ""CarEquipmentPrice"": 2000, ""YearOfPurchase"": 2024, ""IsLeased"": true, ""KmPerYear"": 10000, ""CarUsage"": 1, ""CarGarage"": 1, ""CarRegistrationCanton"": ""BL"", ""CarTypeCertificate"": ""1TB530"", ""LicenseCanton"": """", ""LicenseNumber"": """"}, ""Driver"": {""BirthDate"": ""2006-06-09T00:00:00.000Z"", ""Gender"": 2, ""TownId"": ""17643"", ""Nationality"": ""FR"", ""ResidencePermit"": 3, ""IsDriverInsuranceTaker"": true, ""LicenseDate"": ""2024-06-09T00:00:00.000Z"", ""IsOtherDriverUnder25"": false, ""CurrentProvider"": 99999}, ""Coverages"": {""CoverageType"": 3, ""RetentionPartialCover"": 500, ""RetentionCollisionCascoCover"": 2000, ""HasBonusProtection"": true, ""HasParkingDamage"": true, ""HasParkingDamageUnlimited"": true, ""HasPersonalEffects"": true, ""HasPassengerAccident"": false, ""WantsPreselection"": true, ""HasGrossNegligence"": true, ""HasAssistance"": false}, ""ClaimsAndConvictionsQuestions"": {""HasLiabilityDamage"": false, ""HasCascoAndParkingDamage"": false, ""HasDrivingLicenseSuspension"": false, ""HasImprisonmentRecords"": false, ""HasRequestRejected"": false, ""HasTerminatedByInsurance"": false}}",1,2025-06-11,ComparisEFAG_test.csv,June25,
1,success,,"{""ResultList"": [{""ResultItemDetail"": {""LiabilityCoverPremium"": 1835.4, ""LiabilityCoverDeductibleYoungDriver"": 1500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Basler_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": null, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Basler_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 98.1}, ""ProductId"": 61, ""ProviderId"": 23, ""ProviderNameInfobase"": ""AV8_DB_Provider_BaloiseDirect"", ""ProductNameInfobase"": ""AV8_Productname_baloisedirect_medium_Tabtop"", ""LogoName"": ""Basler_rwxd0n"", ""ComparisSatisfactionGrade"": 5.2, ""ComparisAward"": 2, ""IsWinner"": false, ""OfferQuote"": 1933.5, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV8_DB_footnote_baloisedirect.ch_Tabtop""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""baloise"", ""ProductName"": ""baloisedirect m"", ""ProductType"": ""Standard"", ""ProductPosition"": 1, ""ProductId"": ""61"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 1931.6, ""LiabilityCoverDeductibleYoungDriver"": 1500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": null, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 103.1}, ""ProductId"": 75, ""ProviderId"": 2, ""ProviderNameInfobase"": ""AV8_DB_Provider_Tcs"", ""ProductNameInfobase"": ""AV8_Productname_TCS_M_Tabtop"", ""LogoName"": ""TCS_logo_dklppf"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2034.7, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_TCS_Partnergarage""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""touring club schweiz (tcs)"", ""ProductName"": ""m \u2013 die smarte wahl"", ""ProductType"": ""Standard"", ""ProductPosition"": 2, ""ProductId"": ""75"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 297.5, ""LiabilityCoverDeductibleYoungDriver"": 2000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Allianz_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 1000.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1644.2, ""ComprehensiveCoverDeductibleYoungDriver"": 2000, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Allianz_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 103.5}, ""ProductId"": 57, ""ProviderId"": 8, ""ProviderNameInfobase"": ""AV8_DB_Provider_AllianzSuisse"", ""ProductNameInfobase"": ""AV8_Productname_Allianz_Budget_Tabtop"", ""LogoName"": ""Allianz_cgzi2h"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2045.2, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AZElvia""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""allianz suisse"", ""ProductName"": ""smart"", ""ProductType"": ""Standard"", ""ProductPosition"": 3, ""ProductId"": ""57"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 248.1, ""LiabilityCoverDeductibleYoungDriver"": 3000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1718.75, ""ComprehensiveCoverDeductibleYoungDriver"": 3000, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 104.68}, ""ProductId"": 1085, ""ProviderId"": 1, ""ProviderNameInfobase"": ""AV8_DB_Provider_AxAWinterthur"", ""ProductNameInfobase"": ""AV_Productname_AXA_FLEX"", ""LogoName"": ""AXA_n5xnpq"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2071.53, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AXAPartnerGarage""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""axa"", ""ProductName"": ""flex"", ""ProductType"": ""Standard"", ""ProductPosition"": 4, ""ProductId"": ""1085"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 328.9, ""LiabilityCoverDeductibleYoungDriver"": 1500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Allianz_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1644.2, ""ComprehensiveCoverDeductibleYoungDriver"": 2000, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Allianz_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 105.3}, ""ProductId"": 73, ""ProviderId"": 8, ""ProviderNameInfobase"": ""AV8_DB_Provider_AllianzSuisse"", ""ProductNameInfobase"": ""AV8_Productname_Allianz_Autoversicherung"", ""LogoName"": ""Allianz_cgzi2h"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2078.4, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AZElvia""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""allianz suisse"", ""ProductName"": ""premium"", ""ProductType"": ""Standard"", ""ProductPosition"": 5, ""ProductId"": ""73"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 252.0, ""LiabilityCoverDeductibleYoungDriver"": 2000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1786.85, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 108.31}, ""ProductId"": 1086, ""ProviderId"": 1, ""ProviderNameInfobase"": ""AV8_DB_Provider_AxAWinterthur"", ""ProductNameInfobase"": ""AV_Productname_AXA_FLEXPlus"", ""LogoName"": ""AXA_n5xnpq"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2147.16, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AXAPartnerGarage""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""axa"", ""ProductName"": ""flex plus"", ""ProductType"": ""Standard"", ""ProductPosition"": 6, ""ProductId"": ""1086"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 308.9, ""LiabilityCoverDeductibleYoungDriver"": 2000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_ELVIA_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 1000.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1860.3, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_ELVIA_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 114.9}, ""ProductId"": 56, ""ProviderId"": 25, ""ProviderNameInfobase"": ""AV8_DB_Provider_Elvia"", ""ProductNameInfobase"": ""AV8_Productname_Allianz24_Small_Tabtop"", ""LogoName"": ""Elvia_a2ay99"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2284.1, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AZElvia""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""elvia by allianz"", ""ProductName"": ""fair"", ""ProductType"": ""Standard"", ""ProductPosition"": 7, ""ProductId"": ""56"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 341.5, ""LiabilityCoverDeductibleYoungDriver"": 1500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_ELVIA_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1860.3, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_ELVIA_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 116.9}, ""ProductId"": 29, ""ProviderId"": 25, ""ProviderNameInfobase"": ""AV8_DB_Provider_Elvia"", ""ProductNameInfobase"": ""AV8_Productname_Allianz24_Tabtop"", ""LogoName"": ""Elvia_a2ay99"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2318.7, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AZElvia""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""elvia by allianz"", ""ProductName"": ""nice"", ""ProductType"": ""Standard"", ""ProductPosition"": 8, ""ProductId"": ""29"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 654.9, ""LiabilityCoverDeductibleYoungDriver"": 2500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 362.9, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1240.8, ""ComprehensiveCoverDeductibleYoungDriver"": 3000, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 121.4}, ""ProductId"": 1076, ""ProviderId"": 6, ""ProviderNameInfobase"": ""AV8_DB_Provider_Zurich"", ""ProductNameInfobase"": ""AV8_Productname_ZurichOptima_Tabtop"", ""LogoName"": ""Zurich_lvhgcv"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2407.7, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV8_DB_footnote_ZurichConnect_Tabtop""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""zurich"", ""ProductName"": ""optimum"", ""ProductType"": ""Standard"", ""ProductPosition"": 9, ""ProductId"": ""1076"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 967.1, ""LiabilityCoverDeductibleYoungDriver"": 1000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Helvetia_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1306.7, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Helvetia_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 121.8}, ""ProductId"": 44, ""ProviderId"": 10, ""ProviderNameInfobase"": ""AV8_DB_Provider_Helvetia"", ""ProductNameInfobase"": ""AV8_Productname_Helvetia_Premium_Tabtop"", ""LogoName"": ""Helvetia_r6pgkq"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2410.6, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV8_DB_footnote_Helvetia_Taptop""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""helvetia"", ""ProductName"": ""autoversicherung plus"", ""ProductType"": ""Standard"", ""ProductPosition"": 10, ""ProductId"": ""44"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 670.8, ""LiabilityCoverDeductibleYoungDriver"": 2000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 0.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 362.9, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1240.8, ""ComprehensiveCoverDeductibleYoungDriver"": 3000, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 122.3}, ""ProductId"": 59, ""ProviderId"": 6, ""ProviderNameInfobase"": ""AV8_DB_Provider_Zurich"", ""ProductNameInfobase"": ""AV8_Productname_ZurichFlex_Tabtop"", ""LogoName"": ""Zurich_lvhgcv"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2424.5, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV8_DB_footnote_ZurichConnect_Tabtop""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""zurich"", ""ProductName"": ""premium"", ""ProductType"": ""Standard"", ""ProductPosition"": 11, ""ProductId"": ""59"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 589.4, ""LiabilityCoverDeductibleYoungDriver"": 0.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 0.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 1122.1, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 846.1, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 148.7}, ""ProductId"": 68, ""ProviderId"": 9, ""ProviderNameInfobase"": ""AV8_DB_Provider_Generali"", ""ProductNameInfobase"": ""AV8_Productname_Generali_Optima_Tabtop"", ""LogoName"": ""Generali_qawnha"", ""ComparisSatisfactionGrade"": 5.0, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 2886.8, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": null, ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""generali"", ""ProductName"": ""optima"", ""ProductType"": ""Standard"", ""ProductPosition"": 12, ""ProductId"": ""68"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 8, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 1}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 2751.0, ""LiabilityCoverDeductibleYoungDriver"": 1000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Basler_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 0.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 0, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": false, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": null, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Basler_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 500, ""IsRequestedComprehensiveCoverDeductible"": false, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 141.7}, ""ProductId"": 62, ""ProviderId"": 23, ""ProviderNameInfobase"": ""AV8_DB_Provider_BaloiseDirect"", ""ProductNameInfobase"": ""AV8_Productname_baloisedirect_large_Tabtop"", ""LogoName"": ""Basler_rwxd0n"", ""ComparisSatisfactionGrade"": 5.2, ""ComparisAward"": 2, ""IsWinner"": false, ""OfferQuote"": 2892.7, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV8_DB_footnote_baloisedirect.ch_Tabtop""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""baloise"", ""ProductName"": ""baloisedirect l"", ""ProductType"": ""Standard"", ""ProductPosition"": 13, ""ProductId"": ""62"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 2857.3, ""LiabilityCoverDeductibleYoungDriver"": 1000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 0.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 0, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": false, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": null, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 500, ""IsRequestedComprehensiveCoverDeductible"": false, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 147.1}, ""ProductId"": 76, ""ProviderId"": 2, ""ProviderNameInfobase"": ""AV8_DB_Provider_Tcs"", ""ProductNameInfobase"": ""AV8_Productname_TCS_L_Tabtop"", ""LogoName"": ""TCS_logo_dklppf"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 3004.4, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_TCS_Partnergarage""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""touring club schweiz (tcs)"", ""ProductName"": ""l \u2013 von allem das beste"", ""ProductType"": ""Standard"", ""ProductPosition"": 14, ""ProductId"": ""76"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 536.4, ""LiabilityCoverDeductibleYoungDriver"": 0.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 0.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 1304.7, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 983.8, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 159.9}, ""ProductId"": 67, ""ProviderId"": 9, ""ProviderNameInfobase"": ""AV8_DB_Provider_Generali"", ""ProductNameInfobase"": ""AV8_Productname_Generali_Classic_Tabtop"", ""LogoName"": ""Generali_qawnha"", ""ComparisSatisfactionGrade"": 5.0, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 3185.9, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": null, ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""generali"", ""ProductName"": ""classic"", ""ProductType"": ""Standard"", ""ProductPosition"": 15, ""ProductId"": ""67"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 8, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 1}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 388.9, ""LiabilityCoverDeductibleYoungDriver"": 1000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Smile_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 2691.9, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Smile_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 161.1}, ""ProductId"": 10, ""ProviderId"": 5, ""ProviderNameInfobase"": ""AV8_DB_Provider_smiledirect"", ""ProductNameInfobase"": ""AV8_Productname_Smile_Clever_Tabtop"", ""LogoName"": ""Smile_alkmtg"", ""ComparisSatisfactionGrade"": 5.2, ""ComparisAward"": 2, ""IsWinner"": false, ""OfferQuote"": 3241.9, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_Smile""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""smile"", ""ProductName"": ""clever"", ""ProductType"": ""Standard"", ""ProductPosition"": 16, ""ProductId"": ""10"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 5, ""TitleInfobaseKey"": ""AV_ResultPage_DetailSheet_30D"", ""DescriptionInfobaseKey"": ""AV_ResultPage_DetailSheet_30DInfoText"", ""IconName"": ""faFileTimes"", ""Visibility"": 2}, {""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 430.5, ""LiabilityCoverDeductibleYoungDriver"": 1000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Smile_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 2844.2, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Smile_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 171.1}, ""ProductId"": 31, ""ProviderId"": 5, ""ProviderNameInfobase"": ""AV8_DB_Provider_smiledirect"", ""ProductNameInfobase"": ""AV8_Productname_Smile_premium_Tabtop"", ""LogoName"": ""Smile_alkmtg"", ""ComparisSatisfactionGrade"": 5.2, ""ComparisAward"": 2, ""IsWinner"": false, ""OfferQuote"": 3445.8, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_Smile""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 0, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""smile"", ""ProductName"": ""premium"", ""ProductType"": ""Standard"", ""ProductPosition"": 17, ""ProductId"": ""31"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Matching Result List""}, ""ResultCategory"": 5, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 5, ""TitleInfobaseKey"": ""AV_ResultPage_DetailSheet_30D"", ""DescriptionInfobaseKey"": ""AV_ResultPage_DetailSheet_30DInfoText"", ""IconName"": ""faFileTimes"", ""Visibility"": 2}, {""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 1194.2, ""LiabilityCoverDeductibleYoungDriver"": 1500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Basler_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": false, ""HasComprehensiveBonusCover"": false, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": false, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": null, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Basler_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": false, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 65.8}, ""ProductId"": 60, ""ProviderId"": 23, ""ProviderNameInfobase"": ""AV8_DB_Provider_BaloiseDirect"", ""ProductNameInfobase"": ""AV8_Productname_baloisedirect_small_Tabtop"", ""LogoName"": ""Basler_rwxd0n"", ""ComparisSatisfactionGrade"": 5.2, ""ComparisAward"": 2, ""IsWinner"": false, ""OfferQuote"": 1260.0, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV8_DB_footnote_baloisedirect.ch_Tabtop""], ""BonusProtectionCoverageMark"": 2, ""ParkingDamagesCoverageMark"": 2, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""baloise"", ""ProductName"": ""baloisedirect s"", ""ProductType"": ""Standard"", ""ProductPosition"": 1, ""ProductId"": ""60"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Non Matching Result List""}, ""ResultCategory"": 6, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 1259.3, ""LiabilityCoverDeductibleYoungDriver"": 1500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": false, ""HasComprehensiveBonusCover"": false, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": false, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": null, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": false, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 69.2}, ""ProductId"": 74, ""ProviderId"": 2, ""ProviderNameInfobase"": ""AV8_DB_Provider_Tcs"", ""ProductNameInfobase"": ""AV8_Productname_TCS_Tabtop"", ""LogoName"": ""TCS_logo_dklppf"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 1328.5, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_TCS_Partnergarage""], ""BonusProtectionCoverageMark"": 2, ""ParkingDamagesCoverageMark"": 2, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""touring club schweiz (tcs)"", ""ProductName"": ""s \u2013 sparen beim fahren"", ""ProductType"": ""Standard"", ""ProductPosition"": 2, ""ProductId"": ""74"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Non Matching Result List""}, ""ResultCategory"": 6, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 248.1, ""LiabilityCoverDeductibleYoungDriver"": 3000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": false, ""HasComprehensiveBonusCover"": false, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1045.07, ""ComprehensiveCoverDeductibleYoungDriver"": 3000, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_AXA_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 1000, ""IsRequestedComprehensiveCoverDeductible"": true, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": false, ""HasParkingDamage"": false, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 70.72}, ""ProductId"": 1087, ""ProviderId"": 1, ""ProviderNameInfobase"": ""AV8_DB_Provider_AxAWinterthur"", ""ProductNameInfobase"": ""AV_Productname_AXA_FLEXMinima"", ""LogoName"": ""AXA_n5xnpq"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 1363.89, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_AXAPartnerGarage""], ""BonusProtectionCoverageMark"": 2, ""ParkingDamagesCoverageMark"": 2, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 0, ""PersonalEffectsCoverageMark"": 2, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""axa"", ""ProductName"": ""flex minima"", ""ProductType"": ""Standard"", ""ProductPosition"": 3, ""ProductId"": ""1087"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Non Matching Result List""}, ""ResultCategory"": 6, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 387.1, ""LiabilityCoverDeductibleYoungDriver"": 1000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Helvetia_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": false, ""HasComprehensiveBonusCover"": false, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 500, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": false, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1059.6, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Helvetia_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": false, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": false, ""HasParkingDamage"": false, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 79.4}, ""ProductId"": 45, ""ProviderId"": 10, ""ProviderNameInfobase"": ""AV8_DB_Provider_Helvetia"", ""ProductNameInfobase"": ""AV8_Productname_Helvetia_Budget_Tabtop"", ""LogoName"": ""Helvetia_r6pgkq"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 1526.1, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV8_DB_footnote_Helvetia_Taptop""], ""BonusProtectionCoverageMark"": 2, ""ParkingDamagesCoverageMark"": 2, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 3, ""PersonalEffectsCoverageMark"": 2, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""helvetia"", ""ProductName"": ""autoversicherung economy"", ""ProductType"": ""Standard"", ""ProductPosition"": 4, ""ProductId"": ""45"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Non Matching Result List""}, ""ResultCategory"": 6, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 487.2, ""LiabilityCoverDeductibleYoungDriver"": 500.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 279.2, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 609.2, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Generali_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": false, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": false, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 84.0}, ""ProductId"": 66, ""ProviderId"": 9, ""ProviderNameInfobase"": ""AV8_DB_Provider_Generali"", ""ProductNameInfobase"": ""AV8_Productname_Generali_Minima_Tabtop"", ""LogoName"": ""Generali_qawnha"", ""ComparisSatisfactionGrade"": 5.0, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 1599.7, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": null, ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 2, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 3, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""generali"", ""ProductName"": ""minima"", ""ProductType"": ""Standard"", ""ProductPosition"": 5, ""ProductId"": ""66"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Non Matching Result List""}, ""ResultCategory"": 6, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 8, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Freegarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 1}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 378.4, ""LiabilityCoverDeductibleYoungDriver"": 3000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 1000.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": 362.9, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1091.8, ""ComprehensiveCoverDeductibleYoungDriver"": 4000, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Zurich_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": false, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": false, ""HasParkingDamage"": false, ""HasParkingDamageLimited"": false, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 98.7}, ""ProductId"": 58, ""ProviderId"": 6, ""ProviderNameInfobase"": ""AV8_DB_Provider_Zurich"", ""ProductNameInfobase"": ""AV8_Productname_ZurichBasic_Tabtop"", ""LogoName"": ""Zurich_lvhgcv"", ""ComparisSatisfactionGrade"": 5.1, ""ComparisAward"": 1, ""IsWinner"": false, ""OfferQuote"": 1931.8, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV8_DB_footnote_ZurichConnect_Tabtop""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 2, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 3, ""PersonalEffectsCoverageMark"": 2, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""zurich"", ""ProductName"": ""basic"", ""ProductType"": ""Standard"", ""ProductPosition"": 6, ""ProductId"": ""58"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Non Matching Result List""}, ""ResultCategory"": 6, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}, {""ResultItemDetail"": {""LiabilityCoverPremium"": 388.9, ""LiabilityCoverDeductibleYoungDriver"": 1000.0, ""LiabilityCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Smile_YoungDriver_Tabtop"", ""LiabilityCoverDeductibleOtherDriver"": 500.0, ""HasLiabilityBonusCover"": true, ""HasComprehensiveBonusCover"": true, ""HasPartialCover"": true, ""PartialCoverPremium"": null, ""PartialCoverDeductible"": 1000, ""RequestedPartialCoverDeductible"": 1000.0, ""IsRequestedPartialCoverDeductible"": true, ""HasComprehensiveCover"": true, ""ComprehensiveCoverPremium"": 1853.2, ""ComprehensiveCoverDeductibleYoungDriver"": null, ""ComprehensiveCoverYoungDriverInfobaseKey"": ""AV_Result_Franchise_Smile_YoungDriver_Tabtop"", ""ComprehensiveCoverDeductibleOtherDriver"": 2000, ""IsRequestedComprehensiveCoverDeductible"": false, ""RequestedComprehensiveCoverDeductible"": 1000.0, ""HasPersonalEffects"": true, ""HasParkingDamage"": true, ""HasParkingDamageLimited"": true, ""HasGrossNegligence"": false, ""HasAssistance"": false, ""HasOccupantProtection"": false, ""OccupantProtectionPremium"": null, ""SatutoryCharges"": 119.2}, ""ProductId"": 30, ""ProviderId"": 5, ""ProviderNameInfobase"": ""AV8_DB_Provider_smiledirect"", ""ProductNameInfobase"": ""AV8_Productname_Smile_budget_Tabtop"", ""LogoName"": ""Smile_alkmtg"", ""ComparisSatisfactionGrade"": 5.2, ""ComparisAward"": 2, ""IsWinner"": false, ""OfferQuote"": 2361.3, ""OfferButtonType"": 1, ""FootnoteIndexValue"": null, ""SpecialOfferInfobase"": null, ""PremiumFirstYear"": null, ""PremiumFirstYearDiscount"": null, ""Footnotes"": [""AV_ResultPage_Footnote_Smile""], ""BonusProtectionCoverageMark"": 0, ""ParkingDamagesCoverageMark"": 3, ""GrossNegligenceCoverageMark"": 2, ""AssistanceCoverageMark"": 2, ""RetentionCoverageMark"": 3, ""PersonalEffectsCoverageMark"": 0, ""OccupantProtectionCoverageMark"": 0, ""EcommerceItemModel"": {""Event"": ""productImpressions"", ""Provider"": ""smile"", ""ProductName"": ""budget"", ""ProductType"": ""Standard"", ""ProductPosition"": 7, ""ProductId"": ""30"", ""ProductState"": """", ""RelatedProduct"": null, ""ListName"": ""Car Insurance - Non Matching Result List""}, ""ResultCategory"": 6, ""IsDirectBuyAvailable"": false, ""HasMileagePricing"": false, ""IsTopBoxAd"": false, ""Benefits"": [{""BenefitID"": 5, ""TitleInfobaseKey"": ""AV_ResultPage_DetailSheet_30D"", ""DescriptionInfobaseKey"": ""AV_ResultPage_DetailSheet_30DInfoText"", ""IconName"": ""faFileTimes"", ""Visibility"": 2}, {""BenefitID"": 7, ""TitleInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage"", ""DescriptionInfobaseKey"": ""AV_Resultpage_Benefit_Partnergarage_Description"", ""IconName"": ""faWrench"", ""Visibility"": 3}]}], ""IsFinished"": true}","{""Header"": {""Language"": ""en""}, ""UserEmail"": """", ""OptimatisToggleState"": true, ""InsuranceStartDate"": ""2025-09-09T15:59:23.000Z"", ""CarInput"": {""ImmatriculationMonth"": 6, ""ImmatriculationYear"": 2025, ""SearchMode"": 3, ""CarMake"": 118931, ""CarModelSeries"": 125027, ""CarModel"": 215073, ""CarGearType"": 180010, ""CarType"": 102231505, ""CarEquipmentPrice"": 2000, ""YearOfPurchase"": 2024, ""IsLeased"": true, ""KmPerYear"": 10000, ""CarUsage"": 4, ""CarGarage"": 1, ""CarRegistrationCanton"": ""FR"", ""CarTypeCertificate"": ""ABA378"", ""LicenseCanton"": """", ""LicenseNumber"": """"}, ""Driver"": {""BirthDate"": ""1963-06-09T00:00:00.000Z"", ""Gender"": 1, ""TownId"": ""17196"", ""Nationality"": ""CH"", ""ResidencePermit"": null, ""IsDriverInsuranceTaker"": true, ""LicenseDate"": ""2013-06-09T00:00:00.000Z"", ""IsOtherDriverUnder25"": false, ""CurrentProvider"": 99999}, ""Coverages"": {""CoverageType"": 3, ""RetentionPartialCover"": 1000, ""RetentionCollisionCascoCover"": 1000, ""HasBonusProtection"": true, ""HasParkingDamage"": true, ""HasParkingDamageUnlimited"": true, ""HasPersonalEffects"": true, ""HasPassengerAccident"": false, ""WantsPreselection"": true, ""HasGrossNegligence"": false, ""HasAssistance"": false}, ""ClaimsAndConvictionsQuestions"": {""HasLiabilityDamage"": false, ""HasCascoAndParkingDamage"": false, ""HasDrivingLicenseSuspension"": false, ""HasImprisonmentRecords"": false, ""HasRequestRejected"": false, ""HasTerminatedByInsurance"": false}}",1,2025-06-11,ComparisEFAG_test.csv,June25,
