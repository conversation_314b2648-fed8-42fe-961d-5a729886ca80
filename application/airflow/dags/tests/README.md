# TuringTransformer Integration Tests

This directory contains integration tests for the refactored TuringTransformer that validate functionality with real ComparisEFAG data.

## Files

### `integration_test_turing_transformer.py`
Comprehensive integration test suite that validates:
- Data loading and validation
- Column transformations (including displacement mapping)
- Turing API request generation
- Scraper integration
- Sample request output

### `sample_turing_request.json`
Sample Turing API request generated from real ComparisEFAG data for inspection and validation.

## Usage

### Running the Integration Test

From the `dags` directory:

```bash
python tests/integration_test_turing_transformer.py
```

### Expected Output

```
🚀 Starting TuringTransformer integration tests...
📁 Test data source: input_parameters/comparis/comparis_motor/ComparisEFAG_test.csv

==================================================
Running: Data Validation
==================================================
✅ Data validation passed - all required columns present
✅ Data Validation: PASSED

==================================================
Running: Transform and Validate
==================================================
✅ Transform and validate successful: (2, 71)
✅ Transform and Validate: PASSED

==================================================
Running: Displacement Mapping
==================================================
✅ Displacement mapping successful: 1490 → 1490
✅ Displacement Mapping: PASSED

==================================================
Running: Request Generation
==================================================
✅ Request generation successful:
   - Reference ID: 0
   - Answers: 37
   - Priced Elements: 4
   - Product: CHMOTOR v3
✅ Request Generation: PASSED

==================================================
Running: Scraper Integration
==================================================
✅ Scraper integration successful
✅ Scraper Integration: PASSED

==================================================
Running: Batch Transformation Methods
==================================================
✅ Batch transformation methods successful:
   - Quote requests: 2
   - Indicator table: (0, 42)
✅ Batch Transformation Methods: PASSED

==================================================
Running: Batch Response Processing
==================================================
✅ Batch response processing successful:
   - Results count: 2
   - Premium values: []
   - Status values: [nan]
✅ Batch Response Processing: PASSED

==================================================
Running: Process Data Method
==================================================
✅ process_data method validation successful:
   - Batch request structure: Valid
   - Quote requests: 2
   - All requests have required fields
✅ Process Data Method: PASSED

==================================================
TEST SUMMARY
==================================================
Passed: 8/8
Success Rate: 100.0%
🎉 ALL TESTS PASSED! TuringTransformer is working correctly with real data.
```

## Test Data Requirements

The integration test expects:
- `input_parameters/comparis/comparis_motor/ComparisEFAG_test.csv`
- Required columns:
  - `CrawlerID`
  - `DRVAGE`
  - `AGEDRVLICENSE`
  - `What's the time in years since the 1st matriculation date on the market?`
  - `What is the vehicles displacement (Cubic capacity)?`

## What the Tests Validate

### 1. Data Validation
- Verifies all required columns are present in the CSV
- Ensures data can be loaded successfully

### 2. Transform and Validate
- Tests the main `transform_and_validate()` method
- Validates that calculated date columns are added
- Ensures row count is preserved

### 3. Displacement Mapping
- **Critical Test**: Validates that the displacement column renaming works correctly
- Ensures `"What is the vehicles displacement (Cubic capacity)?"` → `DISPLACEMENT`
- Verifies the actual displacement value (e.g., 1490cc) is preserved

### 4. Request Generation
- Tests batch `transform_batch_data()` method
- Validates Turing API request structure
- Ensures all required fields are present:
  - `referenceId`
  - `request.answers` (risk questions)
  - `request.pricedElements` (coverage options)
  - `request.product` (CHMOTOR v3)

### 5. Scraper Integration
- Tests integration with `TuringInsuranceScraper`
- Validates headers generation
- Ensures end-to-end compatibility

### 6. Batch Transformation Methods
- **New**: Tests `transform_batch_data()` method specifically
- Validates batch request generation for multiple rows
- Ensures indicator table is created properly
- Verifies quote request count matches input data

### 7. Batch Response Processing
- **New**: Tests `process_batch_response()` method
- Validates processing of mock Turing API batch responses
- Tests result DataFrame structure and merging with input data
- Ensures proper handling of premium and status information

### 8. Process Data Method
- **New**: Tests the main `process_data()` method
- Validates end-to-end batch processing workflow
- Ensures proper request structure for API calls
- Tests all required fields in generated requests

## Sample Request Structure

The generated `sample_turing_request.json` contains:

```json
{
  "quoteRequests": [
    {
      "referenceId": "0",
      "include": ["QUOTE_DETAILS"],
      "request": {
        "product": {"id": "CHMOTOR", "version": "3"},
        "pricingVersion": "27",
        "answers": {
          "DISPLACEMENT": 1490,
          "VEH_POWER": 68,
          "DRV_NATIONALITY": "FR",
          // ... 34 more risk answers
        },
        "pricedElements": [
          {"id": "267"}, // MTPL
          {"id": "408"}, // MOD
          {"id": "409"}, // Glass Window
          {"id": "410"}  // Glass Plus
        ]
      }
    }
  ]
}
```

## Troubleshooting

### Common Issues

1. **File Not Found Error**
   - Ensure `ComparisEFAG_test.csv` exists in the expected location
   - Check that you're running from the `dags` directory

2. **Missing Columns**
   - Verify the CSV has all required columns
   - Check for typos in column names

3. **Displacement Mapping Failure**
   - This indicates the column renaming logic is not working
   - Check that the displacement column exists in the CSV

### Running Individual Tests

You can modify the test file to run specific tests by commenting out unwanted tests in the `tests` list within `run_all_tests()`.

## Maintenance

This integration test should be run:
- After any changes to `TuringTransformer`
- Before deploying to production
- When updating the ComparisEFAG data format
- As part of CI/CD pipeline validation

The test is designed to be permanent and should **not be deleted** as it provides ongoing validation of the refactored transformer functionality.
