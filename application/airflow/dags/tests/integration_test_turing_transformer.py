#!/usr/bin/env python3
"""
Integration test for TuringTransformer using ComparisEFAG.csv data.

This test validates that the TuringTransformer correctly processes
real production data and generates valid Turing API requests.

Usage:
    python tests/integration_test_turing_transformer.py
    
Expected Input:
    - input_parameters/comparis/comparis_motor/ComparisEFAG_test.csv
    
Output:
    - Validation results logged to console
    - Sample request saved to tests/sample_turing_request.json
"""

import sys
import pandas as pd
import logging
import json
from pathlib import Path
from typing import Optional

# Add the parent directory to Python path for imports
sys.path.append(str(Path(__file__).parent.parent))

from comparis_scraper.transformers.turing_transformer import TuringTransformer
from comparis_scraper.scrapers.turing_insurance_scraper import TuringInsuranceScraper

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TuringTransformerIntegrationTest:
    """Integration test suite for TuringTransformer with real ComparisEFAG data."""

    def __init__(self, csv_path: Optional[str] = None):
        """Initialize the test suite."""
        self.csv_path = csv_path or 'input_parameters/comparis/comparis_motor/ComparisEFAG_test.csv'
        self.test_results = {}
        self.sample_request = None

    def load_test_data(self) -> pd.DataFrame:
        """Load ComparisEFAG test data."""
        try:
            # Resolve path relative to the dags directory
            base_path = Path(__file__).parent.parent
            full_path = base_path / self.csv_path

            if not full_path.exists():
                raise FileNotFoundError(f"Test data file not found: {full_path}")

            data = pd.read_csv(full_path)
            # Remove empty rows
            data = data.dropna(subset=['CrawlerID'])

            logger.info(f"✅ Loaded test data: {len(data)} rows, {len(data.columns)} columns")
            logger.info(f"Test data columns: {list(data.columns)[:10]}...")

            return data

        except Exception as e:
            logger.error(f"❌ Failed to load test data: {str(e)}")
            raise

    def test_data_validation(self, data: pd.DataFrame) -> bool:
        """Validate that the test data has required columns."""
        logger.info("🧪 Testing data validation...")

        required_columns = [
            'CrawlerID',
            'DRVAGE',
            'AGEDRVLICENSE',
            "What's the time in years since the 1st matriculation date on the market?",
            'What is the vehicles displacement (Cubic capacity)?'
        ]

        missing_columns = [col for col in required_columns if col not in data.columns]

        if missing_columns:
            logger.error(f"❌ Missing required columns: {missing_columns}")
            return False

        logger.info("✅ Data validation passed - all required columns present")
        return True

    def test_transform_and_validate(self, data: pd.DataFrame) -> bool:
        """Test the transform_and_validate method with full dataset."""
        logger.info("🧪 Testing transform_and_validate method...")

        try:
            transformed_data = TuringTransformer.transform_and_validate(data)

            # Validate output
            if transformed_data.shape[0] != data.shape[0]:
                logger.error(f"❌ Row count mismatch: input {data.shape[0]}, output {transformed_data.shape[0]}")
                return False

            # Check for required calculated columns
            required_output_columns = [
                'What is the main driver date of birth?',
                'When has the main driver obtained the driving license (date)?',
                'When is the 1st matriculation of the vehicle on the market?'
            ]

            missing_output_columns = [col for col in required_output_columns if col not in transformed_data.columns]
            if missing_output_columns:
                logger.warning(f"⚠️  Missing calculated columns: {missing_output_columns}")

            logger.info(f"✅ Transform and validate successful: {transformed_data.shape}")
            self.test_results['transform_and_validate'] = True
            return True

        except Exception as e:
            logger.error(f"❌ Transform and validate failed: {str(e)}")
            self.test_results['transform_and_validate'] = False
            return False

    def test_displacement_mapping(self, data: pd.DataFrame) -> bool:
        """Test that displacement column is properly mapped."""
        logger.info("🧪 Testing displacement column mapping...")

        try:
            first_row = data.iloc[0].to_dict()
            original_displacement = first_row.get('What is the vehicles displacement (Cubic capacity)?')

            if not original_displacement:
                logger.warning("⚠️  No displacement value in test data")
                return True

            # Transform batch data and check first request
            batch_request, _ = TuringTransformer.transform_batch_data(data.iloc[[0]])

            # Check if DISPLACEMENT is in the answers
            answers = batch_request.get('quoteRequests', [{}])[0].get('request', {}).get('answers', {})
            mapped_displacement = answers.get('DISPLACEMENT')

            if mapped_displacement is None:
                logger.error("❌ DISPLACEMENT not found in request answers")
                return False

            if str(mapped_displacement) != str(original_displacement):
                logger.error(
                    f"❌ Displacement value mismatch: original {original_displacement}, mapped {mapped_displacement}")
                return False

            logger.info(f"✅ Displacement mapping successful: {original_displacement} → {mapped_displacement}")
            self.test_results['displacement_mapping'] = True
            return True

        except Exception as e:
            logger.error(f"❌ Displacement mapping test failed: {str(e)}")
            self.test_results['displacement_mapping'] = False
            return False

    def test_request_generation(self, data: pd.DataFrame) -> bool:
        """Test Turing API request generation."""
        logger.info("🧪 Testing Turing API request generation...")

        try:
            # Transform batch data
            batch_request, _ = TuringTransformer.transform_batch_data(data)

            # Store sample request for inspection
            self.sample_request = batch_request

            # Validate request structure
            if 'quoteRequests' not in batch_request:
                logger.error("❌ Missing 'quoteRequests' in response")
                return False

            quote_requests = batch_request['quoteRequests']
            if not quote_requests:
                logger.error("❌ Empty quote requests")
                return False

            first_quote = quote_requests[0]
            required_fields = ['referenceId', 'request']
            missing_fields = [field for field in required_fields if field not in first_quote]

            if missing_fields:
                logger.error(f"❌ Missing required fields in quote request: {missing_fields}")
                return False

            # Validate request content
            request = first_quote['request']
            if 'answers' not in request or 'pricedElements' not in request:
                logger.error("❌ Missing answers or pricedElements in request")
                return False

            answers_count = len(request['answers'])
            priced_elements_count = len(request['pricedElements'])

            logger.info(f"✅ Request generation successful:")
            logger.info(f"   - Reference ID: {first_quote.get('referenceId')}")
            logger.info(f"   - Answers: {answers_count}")
            logger.info(f"   - Priced Elements: {priced_elements_count}")
            logger.info(
                f"   - Product: {request.get('product', {}).get('id')} v{request.get('product', {}).get('version')}")

            self.test_results['request_generation'] = True
            return True

        except Exception as e:
            logger.error(f"❌ Request generation test failed: {str(e)}")
            self.test_results['request_generation'] = False
            return False

    def test_scraper_integration(self, data: pd.DataFrame) -> bool:
        """Test integration with TuringInsuranceScraper."""
        logger.info("🧪 Testing scraper integration...")

        try:
            # Create scraper instance
            scraper = TuringInsuranceScraper(api_key="test_key_integration")

            # Test headers
            headers = scraper.get_headers()
            expected_headers = ['accept', 'Content-Type', 'api-key']
            missing_headers = [h for h in expected_headers if h not in headers]

            if missing_headers:
                logger.error(f"❌ Missing headers: {missing_headers}")
                return False

            # Test data transformation through scraper
            transformed_data = scraper.transformer.transform_and_validate(data)

            if transformed_data.shape[0] != data.shape[0]:
                logger.error("❌ Scraper transformation changed row count")
                return False

            logger.info("✅ Scraper integration successful")
            self.test_results['scraper_integration'] = True
            return True

        except Exception as e:
            logger.error(f"❌ Scraper integration test failed: {str(e)}")
            self.test_results['scraper_integration'] = False
            return False

    def test_batch_transformation_methods(self, data: pd.DataFrame) -> bool:
        """Test the batch transformation methods in scraper."""
        logger.info("🧪 Testing batch transformation methods...")

        try:
            # Create scraper instance
            scraper = TuringInsuranceScraper(api_key="test_key_batch_transform")

            # Test transform_batch_data method
            batch_request, indicator = scraper.transformer.transform_batch_data(data)

            # Validate batch request structure
            if 'quoteRequests' not in batch_request:
                logger.error("❌ Missing 'quoteRequests' in batch request")
                return False

            quote_requests = batch_request['quoteRequests']
            if len(quote_requests) != len(data):
                logger.error(f"❌ Quote request count mismatch: expected {len(data)}, got {len(quote_requests)}")
                return False

            # Validate indicator table (it might be empty for some configurations)
            if indicator is None:
                logger.error("❌ Indicator table is None")
                return False

            logger.info(f"✅ Batch transformation methods successful:")
            logger.info(f"   - Quote requests: {len(quote_requests)}")
            logger.info(f"   - Indicator table: {indicator.shape}")

            self.test_results['batch_transformation_methods'] = True
            return True

        except Exception as e:
            logger.error(f"❌ Batch transformation methods test failed: {str(e)}")
            self.test_results['batch_transformation_methods'] = False
            return False

    def test_batch_response_processing(self, data: pd.DataFrame) -> bool:
        """Test the process_batch_response method."""
        logger.info("🧪 Testing batch response processing...")

        try:
            # Create scraper instance
            scraper = TuringInsuranceScraper(api_key="test_key_batch_response")

            # Create mock args
            class MockArgs:
                experiment_name = "Integration_Test"
                project_name = "Test_Project"
                md5_hash = "test_hash_integration"

            args = MockArgs()

            # Generate batch request and indicator
            _, indicator = scraper.transformer.transform_batch_data(data)

            # Create mock batch response (similar to real Turing API response)
            mock_batch_response = {
                "quotes": []
            }

            # Add mock quotes for each row in data
            for i, (_, row) in enumerate(data.iterrows()):
                crawler_id = str(row.get('CrawlerID', i))
                mock_quote = {
                    "referenceId": crawler_id,
                    "quote": {
                        "product": {"id": "CHMOTOR", "version": "3"},
                        "pricingVersion": "27",
                        "status": "SUCCESS",
                        "currency": "CHF",
                        "totalPremiums": {
                            "finalPremiums": {
                                "forTotalPeriod": 1200.50 + (i * 100),  # Different premiums
                                "forPaymentPeriod": 100.04 + (i * 8.33)
                            }
                        }
                    }
                }
                mock_batch_response["quotes"].append(mock_quote)

            # Test process_batch_response
            results_df = scraper.process_batch_response(mock_batch_response, data, indicator, args)

            # Validate results
            if results_df.empty:
                logger.error("❌ Empty results DataFrame")
                return False

            expected_columns = ['CrawlerID', 'status', 'currency', 'total_premium']
            missing_columns = [col for col in expected_columns if col not in results_df.columns]
            if missing_columns:
                logger.error(f"❌ Missing columns in results: {missing_columns}")
                return False

            # Check that we have results for all input rows
            if len(results_df) != len(data):
                logger.warning(f"⚠️  Result count mismatch: expected {len(data)}, got {len(results_df)}")

            # Validate premium values
            premium_values = results_df['total_premium'].dropna() if 'total_premium' in results_df.columns else pd.Series()
            if len(premium_values) == 0:
                logger.warning("⚠️  No premium values found - this might be expected for mock responses")
                # Don't fail the test for this, as it might be a mock response limitation
            else:
                logger.info(f"✅ Found {len(premium_values)} premium values")

            logger.info(f"✅ Batch response processing successful:")
            logger.info(f"   - Results count: {len(results_df)}")
            logger.info(f"   - Premium values: {premium_values.tolist()}")
            logger.info(f"   - Status values: {results_df['status'].unique().tolist()}")

            self.test_results['batch_response_processing'] = True
            return True

        except Exception as e:
            logger.error(f"❌ Batch response processing test failed: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            self.test_results['batch_response_processing'] = False
            return False

    def test_process_data_method(self, data: pd.DataFrame) -> bool:
        """Test the main process_data method (without actual API call)."""
        logger.info("🧪 Testing process_data method...")

        try:
            # Create scraper instance
            scraper = TuringInsuranceScraper(api_key="test_key_process_data")

            # Test that process_data calls the batch transformation correctly
            # We'll test the transformation part without making actual API calls

            # Test batch request generation
            batch_request, _ = scraper.transformer.transform_batch_data(data)

            if 'quoteRequests' not in batch_request:
                logger.error("❌ process_data: Missing quoteRequests in batch request")
                return False

            quote_requests = batch_request['quoteRequests']
            if len(quote_requests) != len(data):
                logger.error(f"❌ process_data: Quote request count mismatch")
                return False

            # Validate that each quote request has the required structure
            for i, quote_request in enumerate(quote_requests):
                required_fields = ['referenceId', 'request']
                missing_fields = [field for field in required_fields if field not in quote_request]
                if missing_fields:
                    logger.error(f"❌ process_data: Missing fields in quote request {i}: {missing_fields}")
                    return False

                # Validate request structure
                request = quote_request['request']
                if 'answers' not in request or 'pricedElements' not in request:
                    logger.error(f"❌ process_data: Missing answers/pricedElements in quote request {i}")
                    return False

            logger.info(f"✅ process_data method validation successful:")
            logger.info(f"   - Batch request structure: Valid")
            logger.info(f"   - Quote requests: {len(quote_requests)}")
            logger.info(f"   - All requests have required fields")

            self.test_results['process_data_method'] = True
            return True

        except Exception as e:
            logger.error(f"❌ process_data method test failed: {str(e)}")
            self.test_results['process_data_method'] = False
            return False

    def save_sample_request(self, output_path: Optional[str] = None) -> bool:
        """Save sample request for inspection."""
        if not self.sample_request:
            logger.warning("⚠️  No sample request to save")
            return False

        try:
            if output_path is None:
                output_path = str(Path(__file__).parent / 'sample_turing_request.json')

            with open(output_path, 'w') as f:
                json.dump(self.sample_request, f, indent=2, default=str)

            logger.info(f"✅ Sample request saved to: {output_path}")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to save sample request: {str(e)}")
            return False

    def run_all_tests(self) -> bool:
        """Run all integration tests."""
        logger.info("🚀 Starting TuringTransformer integration tests...")
        logger.info(f"📁 Test data source: {self.csv_path}")

        try:
            # Load test data
            data = self.load_test_data()

            # Run all tests
            tests = [
                ('Data Validation', lambda: self.test_data_validation(data)),
                ('Transform and Validate', lambda: self.test_transform_and_validate(data)),
                ('Displacement Mapping', lambda: self.test_displacement_mapping(data)),
                ('Request Generation', lambda: self.test_request_generation(data)),
                ('Scraper Integration', lambda: self.test_scraper_integration(data)),
                ('Batch Transformation Methods', lambda: self.test_batch_transformation_methods(data)),
                ('Batch Response Processing', lambda: self.test_batch_response_processing(data)),
                ('Process Data Method', lambda: self.test_process_data_method(data)),
            ]

            passed_tests = 0
            total_tests = len(tests)

            for test_name, test_func in tests:
                logger.info(f"\n{'=' * 50}")
                logger.info(f"Running: {test_name}")
                logger.info(f"{'=' * 50}")

                if test_func():
                    passed_tests += 1
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED")

            # Save sample request
            self.save_sample_request()

            # Summary
            logger.info(f"\n{'=' * 50}")
            logger.info(f"TEST SUMMARY")
            logger.info(f"{'=' * 50}")
            logger.info(f"Passed: {passed_tests}/{total_tests}")
            logger.info(f"Success Rate: {(passed_tests / total_tests) * 100:.1f}%")

            if passed_tests == total_tests:
                logger.info("🎉 ALL TESTS PASSED! TuringTransformer is working correctly with real data.")
                return True
            else:
                logger.error("❌ Some tests failed. Please review the logs above.")
                return False

        except Exception as e:
            logger.error(f"❌ Test suite failed: {str(e)}")
            return False


def main():
    """Main entry point for the integration test."""
    test_suite = TuringTransformerIntegrationTest()
    success = test_suite.run_all_tests()
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
