comparis_motor_insurance_pricing_data:
  schema:
    crawler_id: "int"
    status: "string"
    error: "string"
    results: "string"
    input_request: "string"
    retry_attempts: "int"
    crawler_run_date: "date"
    experiment_filename: "string"
    experiment_name: "string"
    experiment_file_md5_hash: "string"
    iptiq_load_id: "string"
    iptiq_load_date: "string"
    iptiq_execution_date: "string"
stg_comparis_motor_insurance_pricing_data:
  schema:
    crawler_id: "int"
    experiment_name: "string"
    experiment_file_md5_hash: "string"
    status: "string"
    error: "string"
    retry_attempts: "int"
    result_item_detail_json: "string"
    product_id: "string"
    provider_id: "string"
    provider_name_infobase: "string"
    product_name_infobase: "string"
    logo_name: "string"
    comparis_satisfaction_grade: "string"
    comparis_award: "string"
    is_winner: "string"
    offer_quote: "string"
    offer_button_type: "string"
    footnote_index_value: "string"
    special_offer_infobase: "string"
    premium_first_year: "string"
    premium_first_year_discount: "string"
    footnotes_json: "string"
    bonus_protection_coverage_mark: "string"
    parking_damages_coverage_mark: "string"
    gross_negligence_coverage_mark: "string"
    assistance_coverage_mark: "string"
    retention_coverage_mark: "string"
    personal_effects_coverage_mark: "string"
    occupant_protection_coverage_mark: "string"
    ecommerce_item_model_json: "string"
    result_category: "string"
    is_direct_buy_available: "string"
    has_mileage_pricing: "string"
    is_top_box_ad: "string"
    benefits_json: "string"
    liability_cover_premium: "string"
    liability_cover_deductible_young_driver: "string"
    liability_cover_young_driver_infobase_key: "string"
    liability_cover_deductible_other_driver: "string"
    has_liability_bonus_cover: "string"
    has_comprehensive_bonus_cover: "string"
    has_partial_cover: "string"
    partial_cover_premium: "string"
    partial_cover_deductible: "string"
    requested_partial_cover_deductible: "string"
    is_requested_partial_cover_deductible: "string"
    has_comprehensive_cover: "string"
    comprehensive_cover_premium: "string"
    comprehensive_cover_deductible_young_driver: "string"
    comprehensive_cover_young_driver_infobase_key: "string"
    comprehensive_cover_deductible_other_driver: "string"
    is_requested_comprehensive_cover_deductible: "string"
    requested_comprehensive_cover_deductible: "string"
    has_personal_effects: "string"
    has_parking_damage: "string"
    has_parking_damage_limited: "string"
    has_gross_negligence: "string"
    has_assistance: "string"
    has_occupant_protection: "string"
    occupant_protection_premium: "string"
    satutory_charges: "string"
    is_finished: "string"
    iptiq_load_id: "string"
    iptiq_load_date: "string"
    iptiq_execution_date: "string"
