check24_content_insurance_pricing_data:
  schema:
    provider: "string"
    tariff_name: "string"
    price: "double"
    recommendation: "string"
    grade: "string"
    experiment_filename: "string"
    experiment_name: "string"
    project_name: "string"
    tariff: "string"
    index: "bigint"
    url: "string"
    status: "string"
    status_reason: "string"
    crawler_run_date: "date"
    iptiq_load_id: "string"
    iptiq_load_date: "string"
    iptiq_execution_date: "string"
check24_building_insurance_pricing_data:
  schema:
    provider: "string"
    tariff_name: "string"
    price: "double"
    highlight: "string"
    grade: "string"
    experiment_filename: "string"
    experiment_name: "string"
    project_name: "string"
    tariff: "string"
    index: "bigint"
    url: "string"
    status: "string"
    status_reason: "string"
    crawler_run_date: "date"
    iptiq_load_id: "string"
    iptiq_load_date: "string"
    iptiq_execution_date: "string"
