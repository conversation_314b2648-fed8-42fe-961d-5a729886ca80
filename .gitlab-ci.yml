include:
  - project: 'iptiq/data/ci-cd-includes'
    ref: 0.0.17
    file: '/ci-cd-templates.yml'

variables:
  extends: .cicd-include-variables
  PROJECT: ${CI_PROJECT_NAME}
  AIRFLOW_NAMESPACE: "airflow"
  DAGS_BUCKET: '${AWS_REGION}-${DATALAKE_AWS_ACCOUNT_ID}-${ENVIRONMENT_SHORT}-${PLATFORM}-airflow-dags'
  AIRFLOW_ARTIFACT_NAME: 'zip_dags_${CI_PROJECT_NAME}'
  AIRFLOW_ARTIFACT_NAME_EXT: "${AIRFLOW_ARTIFACT_NAME}.zip"

default:
  image: "$BUILD_IMAGE"
  tags:
    - shared-services
  before_script:
    - !reference [ .AWS_LOG_IN, before_script ]

stages:
  - manual-trigger
  - pre-commit-check
  - static-analysis
  - build
  - test
  - nexus_iq
  - deploy_services

airflow_build_artifacts:
  stage: build
  environment:
    name: ${APPLICATION_DEPLOYMENT_ENVIRONMENT}/airflow
  before_script:
    - export PIPELINE_VERSION=$(cat "PIPELINE_VERSION")
  script:
    - cd $CI_PROJECT_DIR/application/airflow/dags/
    - zip -r9 $CI_PROJECT_DIR/$AIRFLOW_ARTIFACT_NAME . -x '*__pycache__*' -x '*.pytest_cache*' -x '*.DS_Store'
    - pip install --no-deps iptiq-data-pipeline==$PIPELINE_VERSION --extra-index-url https://gitlab-ci-token:$<EMAIL>/api/v4/projects/********/packages/pypi/simple -t . #gitleaks:allow
    - zip -r9 $CI_PROJECT_DIR/zip_dags_${CI_PROJECT_NAME} iptiq_data_pipeline
  artifacts:
    name: "$CI_JOB_STAGE-${CI_COMMIT_REF_NAME}"
    expire_in: 1 hour
    paths:
      - $AIRFLOW_ARTIFACT_NAME_EXT
    when: on_success

gemnasium-python-dependency_scanning:
  before_script:
    - echo "Running dependency-scanning"
  needs: [ airflow_build_artifacts ]

nexus_iq:
  extends: .nexus_iq
  variables:
    NEXUS_COMPILE_ARTIFACTS: $AIRFLOW_ARTIFACT_NAME_EXT
  needs: [ "airflow_build_artifacts" ]

deploy_airflow_service:
  stage: deploy_services
  environment:
    name: ${APPLICATION_DEPLOYMENT_ENVIRONMENT}/airflow
  script:
    - export PROJECT_PREFIX="/${ENVIRONMENT_SHORT}/${PLATFORM}/"
    - export BRIGHT_DATE_SSL_CERTIFICATE=$(aws ssm get-parameters --with-decryption --region ${AWS_REGION} --name "${PROJECT_PREFIX}brightdata/webscraper/proxy/ssl_certificate" | jq -r '.Parameters | first | .Value' | base64 -w 0 )
    - export BRIGHT_DATE_WEBSCRAPER_PWD=$(aws ssm get-parameters --with-decryption --region ${AWS_REGION} --name "${PROJECT_PREFIX}brightdata/webscraper/proxy/password" | jq -r '.Parameters | first | .Value' )
    - aws s3 rm s3://${DAGS_BUCKET}/dags/$AIRFLOW_ARTIFACT_NAME_EXT
    - aws s3 cp $CI_PROJECT_DIR/$AIRFLOW_ARTIFACT_NAME_EXT s3://${DAGS_BUCKET}/dags/ --acl=bucket-owner-full-control
    - aws eks update-kubeconfig --region ${AWS_REGION} --name ${EKS_CLUSTER_ID}
    - kubectl create namespace ${AIRFLOW_NAMESPACE} --dry-run -o yaml | kubectl apply -f -
    - $CI_PROJECT_DIR/scripts/deploy.sh airflow-cloud
