comparis_scraper/
├── __init__.py
├── core/
│   ├── __init__.py
│   ├── base_scraper.py
│   ├── client.py
│   └── mappers/
│       ├── __init__.py
│       └── vehicle_mapper.py
├── models/
│   ├── __init__.py
│   ├── config.py
│   ├── request.py
│   └── response.py
├── utils/
│   ├── __init__.py
│   ├── http.py
│   └── validators.py
├── config/
│   ├── __init__.py
│   └── settings.py
├── setup.py
└── requirements.txt